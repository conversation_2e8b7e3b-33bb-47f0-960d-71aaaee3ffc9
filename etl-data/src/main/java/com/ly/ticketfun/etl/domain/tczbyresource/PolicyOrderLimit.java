package com.ly.ticketfun.etl.domain.tczbyresource;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 政策预定限制
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PolicyOrderLimit implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "DbrGuid", type = IdType.AUTO)
    private Long dbrguid;

    /**
     * 资源id
     */
    private Long resourceId;

    /**
     * 政策id
     */
    private Long policyId;

    /**
     * 提前天数
     */
    private Integer inAdvanceDays;

    /**
     * 提前时间
     */
    private LocalDateTime inAdvanceTime;

    /**
     * 提前小时入园
     */
    private Integer inAdvanceHours;

    /**
     * 提前分钟入园
     */
    private Integer inAdvanceMinutes;

    /**
     * 场次类型0开始时间1结束时间
     */
    private Integer sessionType;

    /**
     * 最大购买数量
     */
    private Integer maxBuyNum;

    /**
     * 最小购买数量
     */
    private Integer minBuyNum;

    /**
     * 支付时限
     */
    private Integer payLimitMinute;

    /**
     * 预定天数限制
     */
    private Integer orderDaysLimit;

    /**
     * 创建人
     */
    @TableField("CreateUser")
    private String createuser;

    /**
     * 创建时间
     */
    @TableField("CreateTime")
    private LocalDateTime createtime;

    /**
     * 更新人
     */
    @TableField("UpdateUser")
    private String updateuser;

    /**
     * 更新时间
     */
    @TableField("UpdateTime")
    private LocalDateTime updatetime;

    /**
     * 是否有效
     */
    private Integer rowStatus;


}
