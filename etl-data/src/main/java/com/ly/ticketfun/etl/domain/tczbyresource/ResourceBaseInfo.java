package com.ly.ticketfun.etl.domain.tczbyresource;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ly.localactivity.framework.annotation.model.*;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.localactivity.model.enums.common.DeleteFlagEnum;


/**
 * <p>
 * 资源表，分片键：资源ID
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ResourceBaseInfo")
@TableExtend(desc = "资源表，分片键：资源ID")
public class ResourceBaseInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分片聚合用
     */
    @TableId(value = "DbrGuid", type = IdType.AUTO)
    @TableFieldExtend(desc = "分片聚合用")
    private Long dbrGuid;

    /**
     * 分片ID
     */
    @TableField("RBIId")
    @TableFieldExtend(desc = "分片ID")
    private Long id;

    /**
     * 资源名称
     */
    @TableField("RBIResourceName")
    @TableFieldExtend(desc = "资源名称")
    private String resourceName;

    /**
     * 第三方资源id,不能直接使用。可通过=0判断此资源是否有自签
     */
    @TableField("RBIOuterResourceId")
    @TableFieldExtend(desc = "第三方资源id,不能直接使用。可通过=0判断此资源是否有自签")
    private String outerResourceId;

    /**
     * 资源地址
     */
    @TableField("RBIAddress")
    @TableFieldExtend(desc = "资源地址")
    private String address;

    /**
     * 资源联系电话
     */
    @TableField("RBIResourcePhone")
    @TableFieldExtend(desc = "资源联系电话")
    private String resourcePhone;

    /**
     * 资源类型(0酒店1景区)
     */
    @TableField("RBIResourceType")
    @TableFieldExtend(desc = "资源类型(0酒店1景区)")
    private Integer resourceType;

    /**
     * 资源等级
     */
    @TableField("RBIResourceGrade")
    @TableFieldExtend(desc = "资源等级")
    private Integer resourceGrade;

    /**
     * 景区：开园时间，酒店：营业时间
     */
    @TableField("RBIOpenTime")
    @TableFieldExtend(desc = "景区：开园时间，酒店：营业时间")
    private String openTime;

    /**
     * 排序
     */
    @TableField("RBISort")
    @TableFieldExtend(desc = "排序")
    private Long sort;

    /**
     * 是否是免费景区
     */
    @TableField("RBIIsFree")
    @TableFieldExtend(desc = "是否是免费景区")
    private Integer isFree;

    /**
     * 装修时间
     */
    @TableField("RBIRenovationDate")
    @TableFieldExtend(desc = "装修时间")
    private LocalDateTime renovationDate;

    /**
     * 建造时间
     */
    @TableField("RBIBuiltDate")
    @TableFieldExtend(desc = "建造时间")
    private LocalDateTime builtDate;

    /**
     * 开业时间
     */
    @TableField("RBIEstablishmentDate")
    @TableFieldExtend(desc = "开业时间")
    private LocalDateTime establishmentDate;

    /**
     * 虚拟线路免费景区，不可售卖景区都不能自由组合
     */
    @TableField("RBICanFreeCombination")
    @TableFieldExtend(desc = "虚拟线路免费景区，不可售卖景区都不能自由组合")
    private Integer canFreeCombination;

    /**
     * 省份id
     */
    @TableField("RBIProvinceId")
    @TableFieldExtend(desc = "省份id")
    private Integer provinceId;

    /**
     * 省份名称
     */
    @TableField("RBIProvinceName")
    @TableFieldExtend(desc = "省份名称")
    private String provinceName;

    /**
     * 城市id
     */
    @TableField("RBICityId")
    @TableFieldExtend(desc = "城市id")
    private Integer cityId;

    /**
     * 城市名称
     */
    @TableField("RBICityName")
    @TableFieldExtend(desc = "城市名称")
    private String cityName;

    /**
     * 区/县id
     */
    @TableField("RBISectionId")
    @TableFieldExtend(desc = "区/县id")
    private Integer sectionId;

    /**
     * 区县名称
     */
    @TableField("RBISectionName")
    @TableFieldExtend(desc = "区县名称")
    private String sectionName;

    /**
     * 商圈ID
     */
    @TableField("RBITradeAreaId")
    @TableFieldExtend(desc = "商圈ID")
    private Integer tradeAreaId;

    /**
     * 商圈名称
     */
    @TableField("RBITradeAreaName")
    @TableFieldExtend(desc = "商圈名称")
    private String tradeAreaName;

    /**
     * 百度经度
     */
    @TableField("RBIBLon")
    @TableFieldExtend(desc = "百度经度")
    private BigDecimal bLon;

    /**
     * 百度维度
     */
    @TableField("RBIBLat")
    @TableFieldExtend(desc = "百度维度")
    private BigDecimal bLat;

    /**
     * 谷歌经度
     */
    @TableField("RBIGLon")
    @TableFieldExtend(desc = "谷歌经度")
    private BigDecimal gLon;

    /**
     * 谷歌维度
     */
    @TableField("RBIGLat")
    @TableFieldExtend(desc = "谷歌维度")
    private BigDecimal gLat;

    /**
     * 简短描述
     */
    @TableField("RBISummary")
    @TableFieldExtend(desc = "简短描述")
    private String summary;

    /**
     * 0不可预定，1可预定, 2暂时关闭
     */
    @TableField("RBICanBook")
    @TableFieldExtend(desc = "0不可预定，1可预定, 2暂时关闭")
    private Integer canBook;

    /**
     * 默认主图
     */
    @TableField("RBIImagePath")
    @TableFieldExtend(desc = "默认主图")
    private String imagePath;

    /**
     * 项目属性
     */
    @TableField("RBIProjectAttribute")
    @TableFieldExtend(desc = "项目属性")
    private Integer projectAttribute;

    /**
     * 是否开放(1 开放 0 不开放)
     */
    @TableField("RBIIsOpen")
    @TableFieldExtend(desc = "是否开放(1 开放 0 不开放)")
    private Integer isOpen;

    /**
     * 二级主题ID
     */
    @TableField("RBIThemeID")
    @TableFieldExtend(desc = "二级主题ID")
    private Integer themeID;

    /**
     * 二级主题名称
     */
    @TableField("RBIThemeName")
    @TableFieldExtend(desc = "二级主题名称")
    private String themeName;

    /**
     * 是否屏蔽搜索
     */
    @TableField("RBIIsShieldSearch")
    @TableFieldExtend(desc = "是否屏蔽搜索")
    private Integer isShieldSearch;

    /**
     * url采用自适应的页面
     */
    @TableField("RBINoticeUrl")
    @TableFieldExtend(desc = "url采用自适应的页面")
    private String noticeUrl;

    /**
     * 全景url
     */
    @TableField("RBIPanoramaUrl")
    @TableFieldExtend(desc = "全景url")
    private String panoramaUrl;

    /**
     * 视频链接
     */
    @TableField("RBIVideoUrl")
    @TableFieldExtend(desc = "视频链接")
    private String videoUrl;

    /**
     * 原始视频链接
     */
    @TableField("RBIOriginalVideoUrl")
    @TableFieldExtend(desc = "原始视频链接")
    private String originalVideoUrl;

    /**
     * 备注
     */
    @TableField("RBIRemark")
    @TableFieldExtend(desc = "备注")
    private String remark;

    /**
     * （plus、app）最低价对应的同程价
     */
    @TableField("RBIMinAmount")
    @TableFieldExtend(desc = "（plus、app）最低价对应的同程价")
    private BigDecimal minAmount;

    /**
     * 有效性
     */
    @TableField("RBIRowStatus")
    @TableFieldExtend(desc = "有效性")
    private Integer rowStatus;

    /**
     * plus最低价,app最低价
     */
    @TableField("RBIMinSalesAmount")
    @TableFieldExtend(desc = "plus最低价,app最低价")
    private BigDecimal minSalesAmount;

    /**
     * pc端最低门市价
     */
    @TableField("RBIMinPcAmount")
    @TableFieldExtend(desc = "pc端最低门市价")
    private BigDecimal minPcAmount;

    /**
     * pc端最低同程价
     */
    @TableField("RBIMinPcSalesAmount")
    @TableFieldExtend(desc = "pc端最低同程价")
    private BigDecimal minPcSalesAmount;

    /**
     * 联盟最低门市价
     */
    @TableField("RBIMinAllianceAmount")
    @TableFieldExtend(desc = "联盟最低门市价")
    private BigDecimal minAllianceAmount;

    /**
     * 联盟最低同程价
     */
    @TableField("RBIMinAllianceSalesAmount")
    @TableFieldExtend(desc = "联盟最低同程价")
    private BigDecimal minAllianceSalesAmount;

    /**
     * 新增人工号[姓名]
     */
    @TableField("CreateUser")
    @TableFieldExtend(desc = "新增人工号[姓名]")
    private String createUser;

    /**
     * 新增时间
     */
    @TableField(value = "CreateTime", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "新增时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 修改人工号[姓名]
     */
    @TableField("UpdateUser")
    @TableFieldExtend(desc = "修改人工号[姓名]")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField(value = "UpdateTime", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "修改时间")
    @ModifyTimeField
    private LocalDateTime updateTime;

    /**
     * 目的地类别
     */
    @TableField("RBIResourceCategory")
    @TableFieldExtend(desc = "目的地类别")
    private Integer resourceCategory;

    /**
     * 同程资源绑定的外部POI站点Id，美团小程序=26，美团app=24，携程=4,如同时绑定了美团小程序和携程的字符串格式为 “ ,26,4, ”，没有绑定关系则为空
     */
    @TableField("RBIPOIBindSiteId")
    @TableFieldExtend(desc = "同程资源绑定的外部POI站点Id，美团小程序=26，美团app=24，携程=4,如同时绑定了美团小程序和携程的字符串格式为 “ ,26,4, ”，没有绑定关系则为空")
    private String pOIBindSiteId;

    /**
     * 合作类型 （已废弃，请使用新表TCZBYResource .ResourceBaseCooperationTypeRelation）
     */
    @TableField("RBICooperationType")
    @TableFieldExtend(desc = "合作类型 （已废弃，请使用新表TCZBYResource .ResourceBaseCooperationTypeRelation）")
    private String cooperationType;

    /**
     * 景区暂时关闭结束时间
     */
    @TableField("RBIResourceClosedEndTime")
    @TableFieldExtend(desc = "景区暂时关闭结束时间")
    private LocalDateTime resourceClosedEndTime;

    /**
     * 开园时间补充说明
     */
    @TableField("RBIOpenTimeRemarks")
    @TableFieldExtend(desc = "开园时间补充说明")
    private String openTimeRemarks;

    /**
     * 开园时间优先级  0结构化 1补充说明
     */
    @TableField("RBIOpenTimeType")
    @TableFieldExtend(desc = "开园时间优先级  0结构化 1补充说明")
    private Integer openTimeType;

    /**
     * 集团Id（数据字典）
     */
    @TableField("RBIGroupId")
    @TableFieldExtend(desc = "集团Id（数据字典）")
    private Integer groupId;

    /**
     * 游玩项目是否前台置顶 （0：否；1是）
     */
    @TableField("RBIIsTopPlay")
    @TableFieldExtend(desc = "游玩项目是否前台置顶 （0：否；1是）")
    private Integer isTopPlay;

    /**
     * 两周内订单量
     */
    @TableField("RBIOrderCount")
    @TableFieldExtend(desc = "两周内订单量")
    private Integer orderCount;

    /**
     * 开园提醒时间
     */
    @TableField("RBIOpenRemindDate")
    @TableFieldExtend(desc = "开园提醒时间")
    private LocalDateTime openRemindDate;

    /**
     * 景区等级 0：无，1：TA，2：SMA，3：KA，4：SKA
     */
    @TableField("RBIResourceLevel")
    @TableFieldExtend(desc = "景区等级 0：无，1：TA，2：SMA，3：KA，4：SKA")
    private Integer resourceLevel;

    /**
     * 景区营业状态： 0：未知，1: 营业， 2：不营业
     */
    @TableField("RBIBusinessStatus")
    @TableFieldExtend(desc = "景区营业状态： 0：未知，1: 营业， 2：不营业")
    private Integer businessStatus;

    /**
     * 价格实拍图
     */
    @TableField("RBITicketRealPicture")
    @TableFieldExtend(desc = "价格实拍图")
    private String ticketRealPicture;

    /**
     * 是否预约 0 否 1是
     */
    @TableField("RBIIsAppointment")
    @TableFieldExtend(desc = "是否预约 0 否 1是")
    private Integer isAppointment;

    /**
     * 国家id
     */
    @TableField("RBICountryId")
    @TableFieldExtend(desc = "国家id")
    private Integer countryId;

    /**
     * 国家名称
     */
    @TableField("RBICountryName")
    @TableFieldExtend(desc = "国家名称")
    private String countryName;

    /**
     * 目的地区域属性（29801：国内目的地，含港澳台；29802：国际目的地）
     */
    @TableField("RBIAreaAttribute")
    @TableFieldExtend(desc = "目的地区域属性（29801：国内目的地，含港澳台；29802：国际目的地）")
    private Integer areaAttribute;

    /**
     * 是否开启挂载 1开启 0不开启
     */
    @TableField("RBIOpenMount")
    @TableFieldExtend(desc = "是否开启挂载 1开启 0不开启")
    private Integer openMount;

    /**
     * 门市价
     */
    @TableField("RBIMBLAmount")
    @TableFieldExtend(desc = "门市价")
    private BigDecimal mBLAmount;

    /**
     * 洲id
     */
    @TableField("RBIContinetId")
    @TableFieldExtend(desc = "洲id")
    private Long continetId;

    /**
     * 洲名称
     */
    @TableField("RBIContinetName")
    @TableFieldExtend(desc = "洲名称")
    private String continetName;

    /**
     * 再次压缩后用于搜索列表的视频url
     */
    @TableField("RBIVideoUrl360")
    @TableFieldExtend(desc = "再次压缩后用于搜索列表的视频url")
    private String videoUrl360;


}
