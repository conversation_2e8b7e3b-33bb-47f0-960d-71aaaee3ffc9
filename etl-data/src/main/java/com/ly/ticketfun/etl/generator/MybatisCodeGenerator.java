package com.ly.ticketfun.etl.generator;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.core.exceptions.MybatisPlusException;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.InjectionConfig;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.po.TableField;
import com.baomidou.mybatisplus.generator.config.po.TableFill;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.baomidou.mybatisplus.generator.config.rules.FileType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.ly.localactivity.framework.generator.CodeModule;
import com.ly.localactivity.framework.service.IDbBaseService;
import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.text.MessageFormat;
import java.util.*;
import java.util.regex.Matcher;

/**
 * CodeGenerator
 *
 * <AUTHOR>
 * @date 2021/9/26
 */
public class MybatisCodeGenerator {

    private static final String PACKAGE_NAME = "com.ly.ticketfun.etl";
    private static final String PROJECT_PATH = System.getProperty("user.dir") + "/etl-data";
    private static final String PROJECT_MIDDLEWARE_PATH = System.getProperty("user.dir") + "/etl-data";

    private static final String PROJECT_ADMIN_PATH = System.getProperty("user.dir") + "/etl-producer";

    private static final List<String> tablePrefixList = Arrays.asList("WR", "WC", "WL");
    /**
     * 下划线
     */
    private static final char SEPARATOR = '_';
    private static final String DB_TCZBActivityResource = "TCZBActivityResource";
    private static final String DB_TCZBActivityCommon = "TCZBActivityCommon";
    private static final String DB_TCZBActivityLog = "TCZBActivityLog";
    private static final String DB_TCZBActivityStatistic = "TCZBActivityStatistic";
    private static final String DB_TCZBActivityChat = "TCZBActivityChat";
    private static final String DB_TCZBActivityOrder = "TCZBActivityOrder";
    private static final String DB_TCSceneryCompetitiveResource = "TCSceneryCompetitiveResource";
    private static final String DB_TCZBYResource = "TCZBYResource";

    /**
     * <p>
     * 读取控制台内容
     * </p>
     */
    public static String scanner(String tip) {
        Scanner scanner = new Scanner(System.in);
        StringBuilder help = new StringBuilder();
        help.append("请输入" + tip + "：");
        System.out.println(help.toString());
        if (scanner.hasNext()) {
            String ipt = scanner.next();
            if (StringUtils.isNotBlank(ipt)) {
                return ipt;
            }
        }
        throw new MybatisPlusException("请输入正确的" + tip + "！");
    }

    public static void main(String[] args) {

        String dbName = scanner("请输入库名");
        CodeModule codeModule = getModule(dbName);
        if (codeModule == null) {
            throw new IllegalArgumentException("找不到模块信息");
        }

        // 代码生成器
        AutoGenerator mpg = new AutoGenerator();
        // 全局配置
        GlobalConfig gc = new GlobalConfig();
        gc.setOutputDir(PROJECT_PATH + "/src/main/java");
        gc.setAuthor("auto");
        gc.setOpen(false);
        // gc.setSwagger2(true); 实体属性 Swagger2 注解
        mpg.setGlobalConfig(gc);

        // 数据源配置
        DataSourceConfig dsc = new DataSourceConfig();
        dsc.setUrl(codeModule.getDbUrl());
        // dsc.setSchemaName("public");
        dsc.setDriverName("com.mysql.jdbc.Driver");
        dsc.setUsername(codeModule.getDbUser());
        dsc.setPassword(codeModule.getDbPwd());
        mpg.setDataSource(dsc);

        // 包配置
        PackageConfig pc = new PackageConfig();
//        pc.setModuleName(scanner("模块名"));
//        pc.setParent(PACKAGE_NAME);
        pc.setParent("");
        mpg.setPackageInfo(pc);

        // 自定义配置
        InjectionConfig cfg = new InjectionConfig() {
            @Override
            public void initMap() {
                // to do nothing
            }
        };

        // 如果模板引擎是 freemarker
        String templatePath = "/templates/mapper.xml.ftl";
        // 如果模板引擎是 velocity
        // String templatePath = "/templates/mapper.xml.vm";

        // 自定义输出配置
        List<FileOutConfig> focList = new ArrayList<>();
        // 自定义配置会被优先输出
        focList.add(new FileOutConfig(templatePath) {
            @Override
            public String outputFile(TableInfo tableInfo) {
                // 自定义输出文件名 ， 如果你 Entity 设置了前后缀、此处注意 xml 的名称会跟着发生变化！！
                return PROJECT_PATH + "/src/main/resources/mapper/" + codeModule.getModuleName()
                        + "/" + tableInfo.getEntityName() + "Mapper" + StringPool.DOT_XML;
            }
        });
        /*
        cfg.setFileCreate(new IFileCreate() {
            @Override
            public boolean isCreate(ConfigBuilder configBuilder, FileType fileType, String filePath) {
                // 判断自定义文件夹是否需要创建
                checkDir("调用默认方法创建的目录，自定义目录用");
                if (fileType == FileType.MAPPER) {
                    // 已经生成 mapper 文件判断存在，不想重新生成返回 false
                    return !new File(filePath).exists();
                }
                // 允许生成模板文件
                return true;
            }
        });
        */
        cfg.setFileOutConfigList(focList);
        mpg.setCfg(cfg);

        initInjectionConfig(cfg);

        // 配置模板
        TemplateConfig templateConfig = new TemplateConfig();
        templateConfig.setEntity("/template/entity.java");
        // 配置自定义输出模板
        //指定自定义模板路径，注意不要带上.ftl/.vm, 会根据使用的模板引擎自动识别
        // templateConfig.setEntity("templates/entity2.java");
        // templateConfig.setService();
        // templateConfig.setController();

//        templateConfig.setEntity("/templates/entity.java");
//        templateConfig.setMapper("/templates/mapper.java");
//        templateConfig.setService("/templates/service2.java");
//        templateConfig.setServiceImpl(null);
//        templateConfig.setController("/templates/controller.java");

        templateConfig.setXml(null);
        templateConfig.setController("");
        mpg.setTemplate(templateConfig);

        // 策略配置
        StrategyConfig strategy = new StrategyConfig();
        strategy.setNaming(codeModule.getNamingStrategy());
        strategy.setColumnNaming(codeModule.getNamingStrategy());
        strategy.setEntityLombokModel(true);
        strategy.setRestControllerStyle(true);
        strategy.setEntityTableFieldAnnotationEnable(true);
        strategy.setSuperMapperClass("com.ly.localactivity.framework.datasource.LaBaseMapper");
        strategy.setSuperServiceClass(IDbBaseService.class);
        strategy.setSuperServiceImplClass(DbBaseServiceImpl.class);
        if (StringUtils.isNotBlank(codeModule.getLogicDeleteFieldName())) {
//            strategy.setLogicDeleteFieldName(codeModule.getLogicDeleteFieldName());
        }
        // 公共父类
//        strategy.setSuperControllerClass("你自己的父类控制器,没有就不用设置!");
        //公共字段填充(is_deleted,creator,create_time,modifier,modified_time)
        List<TableFill> tableFillList = new ArrayList<>();
        tableFillList.add(new TableFill("delete_flag", FieldFill.INSERT));
        tableFillList.add(new TableFill("data_flag", FieldFill.INSERT));
        tableFillList.add(new TableFill("creator", FieldFill.INSERT));
        tableFillList.add(new TableFill("creator_no", FieldFill.INSERT));
        tableFillList.add(new TableFill("create_time", FieldFill.INSERT));
        tableFillList.add(new TableFill("modifier", FieldFill.INSERT_UPDATE));
        tableFillList.add(new TableFill("modifier_no", FieldFill.INSERT_UPDATE));
        tableFillList.add(new TableFill("modified_time", FieldFill.INSERT_UPDATE));
        strategy.setTableFillList(tableFillList);
        // 写于父类中的公共字段
        strategy.setInclude(scanner("表名，多个英文逗号分割，不生成controller").split(","));
        strategy.setControllerMappingHyphenStyle(true);
        // 资源库、公共库、日志库表、字段前缀处理
        if (dbName.equals(DB_TCZBActivityResource) || dbName.equals(DB_TCZBActivityCommon) || dbName.equals(DB_TCZBActivityLog)) {
            strategy.setNameConvert(
                    new INameConvert() {
                        @Override
                        public String entityNameConvert(TableInfo tableInfo) {
                            return replaceTablePrefix(tableInfo.getName());
                        }

                        @Override
                        public String propertyNameConvert(TableField field) {
                            field.setConvert(true);
                            return replaceColumnPrefixAndFirstTiLower(field.getName());
                        }
                    });
        }else if (dbName.equals(DB_TCZBYResource)){
            String[] tablePrefixArr = scanner("表名前缀，多个用,分隔").split(",");
            strategy.setNameConvert(new INameConvert() {
                @Override
                public String entityNameConvert(TableInfo tableInfo) {
                    return convertEntityNameForMp(tableInfo.getName());
                }

                @Override
                public String propertyNameConvert(TableField field) {
                    field.setConvert(true);
                    return convertPropertyNameForMp(field.getName(),tablePrefixArr);
                }
            });
        }
        mpg.setStrategy(strategy);
        mpg.setTemplateEngine(new FreemarkerTemplateEngine());
        customPackagePath(pc, mpg, codeModule);
        mpg.execute();
    }

    private static String convertPropertyNameForMp(String name,String[] tablePrefixArr) {
        if (StringUtils.isEmpty(name)){
            return name;
        }
        if (Character.isUpperCase(name.charAt(0))){
            for (String tablePrefix : tablePrefixArr) {
                if (StringUtils.isNotBlank(tablePrefix) && name.startsWith(tablePrefix)){
                    name = name.replace(tablePrefix,"");
                }
            }
            char[] charArray = name.toCharArray();
            charArray[0] = Character.toLowerCase(charArray[0]);
            return new String(charArray);
        }
        if (name.contains("_")){
            char[] charArray = name.toCharArray();
            for (int i = 0; i < charArray.length; i++) {
                if (charArray[i] == '_' && i+1 < charArray.length){
                    charArray[i+1] = Character.toUpperCase(charArray[i+1]);
                }
            }
            return new String(charArray).replaceAll("_","");
        }
        return name;
    }

    private static String convertEntityNameForMp(String name) {
        String entityName = name;
        if (StringUtils.isNotEmpty(name) ){
            if (Character.isUpperCase(name.charAt(0))) {
                return entityName;
            }else if (name.contains("_")){
                char[] charArray = NamingStrategy.underlineToCamel(name).toCharArray();
                charArray[0] = Character.toUpperCase(charArray[0]);
                return new String(charArray);
            }
        }
        return entityName;
    }

    private static void customPackagePath(PackageConfig pc, AutoGenerator mpg, CodeModule codeModule) {

        String mavenPath = MessageFormat.format("{0}src{0}main{0}java{0}", File.separator);
        String srcPath = PROJECT_PATH + File.separator + mavenPath;
        String middlewarePath = PROJECT_MIDDLEWARE_PATH + File.separator + mavenPath;

        //包名
        Map<String, String> packageInfo = new HashMap<>();
//        packageInfo.put(ConstVal.CONTROLLER, PACKAGE_NAME + ".controller." + codeModule.getModuleName());
        packageInfo.put(ConstVal.SERVICE, PACKAGE_NAME + ".dataService." + codeModule.getModuleName());
        packageInfo.put(ConstVal.SERVICE_IMPL, PACKAGE_NAME + ".dataService." + codeModule.getModuleName() + ".impl");
        packageInfo.put(ConstVal.ENTITY, PACKAGE_NAME + ".domain." + codeModule.getModuleName());
        packageInfo.put(ConstVal.MAPPER, PACKAGE_NAME + ".mapper." + codeModule.getModuleName());

//        pc.setController(packageInfo.get(ConstVal.CONTROLLER));
        pc.setEntity(packageInfo.get(ConstVal.ENTITY));
        pc.setService(packageInfo.get(ConstVal.SERVICE));
        pc.setServiceImpl(packageInfo.get(ConstVal.SERVICE_IMPL));
        pc.setMapper(packageInfo.get(ConstVal.MAPPER));

        //路径
        Map pathInfo = new HashMap<>();

//        String module = scanner("请输入controller生成位置，1-admin，2-middleware，默认不生成");
//        if (StringUtils.isNotBlank(module)) {
//            String controllerPath = PROJECT_ADMIN_PATH;
//            if (module.equals("2")) {
//                controllerPath = middlewarePath;
//            }
//            packageInfo.put(ConstVal.CONTROLLER, PACKAGE_NAME + ".controller." + codeModule.getModuleName());
//            pc.setController(packageInfo.get(ConstVal.CONTROLLER));
//            pathInfo.put(ConstVal.CONTROLLER_PATH, controllerPath + packageInfo.get(ConstVal.CONTROLLER).replaceAll("\\.", Matcher.quoteReplacement(File.separator)));
//        }


//        pathInfo.put(ConstVal.CONTROLLER_PATH, controllerPath + packageInfo.get(ConstVal.CONTROLLER).replaceAll("\\.", Matcher.quoteReplacement(File.separator)));
        pathInfo.put(ConstVal.SERVICE_PATH, srcPath + packageInfo.get(ConstVal.SERVICE).replaceAll("\\.", Matcher.quoteReplacement(File.separator)));
        pathInfo.put(ConstVal.SERVICE_IMPL_PATH, srcPath + packageInfo.get(ConstVal.SERVICE_IMPL).replaceAll("\\.", Matcher.quoteReplacement(File.separator)));
        pathInfo.put(ConstVal.ENTITY_PATH, srcPath + packageInfo.get(ConstVal.ENTITY).replaceAll("\\.", Matcher.quoteReplacement(File.separator)));
        pathInfo.put(ConstVal.MAPPER_PATH, srcPath + packageInfo.get(ConstVal.MAPPER).replaceAll("\\.", Matcher.quoteReplacement(File.separator)));
        pc.setPathInfo(pathInfo);
        mpg.setPackageInfo(pc);
    }

    private static void initInjectionConfig(InjectionConfig config) {

        // 如果当前文件已经存在，则仅允许覆盖Entity
        IFileCreate fileCreate = (configBuilder, fileType, filePath) -> {
            if (fileType == FileType.ENTITY) {
                return true;
            }
            //否则先判断文件是否存在
            File file = new File(filePath);
            boolean exist = file.exists();
            if (!exist) {
                file.getParentFile().mkdirs();
            }
            //文件不存在或者全局配置的fileOverride为true才写文件
            return !exist || configBuilder.getGlobalConfig().isFileOverride();
        };
        config.setFileCreate(fileCreate);
    }

    private static CodeModule getModule(String name) {
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(name)) {
            return null;
        }
        if (name.trim().equals("TCZBActDistribution")) {
            return new CodeModule("TCZBActDistribution"
                    , "distribution"
                    , ""
                    , "***************************************************************************************************************************"
                    , "TCZADistribution"
                    , "YIYLhVZSYatq7VWgWzs5H"
                    , NamingStrategy.underline_to_camel
                    , "delete_flag");
        } else if (name.trim().equals(DB_TCZBActivityResource)) {
            return new CodeModule("TCZBActivityResource"
                    , "tczbactivityresource"
                    , "WR"
                    , "*******************************************************************************************************"
                    , "TCZBActResource"
                    , "6Prkq2PIQmhmoJFCzh5Mr"
                    , NamingStrategy.no_change
                    , "");
        } else if (name.trim().equals(DB_TCZBActivityCommon)) {
            return new CodeModule("TCZBActivityCommon"
                    , "tczbactivitycommon"
                    , "WC"
                    , "*****************************************************************************************************"
                    , "TCZBActCommon"
                    , "PtOSLyeJjhAhQcthDfCtKF"
                    , NamingStrategy.no_change
                    , "");
        } else if (name.trim().equals(DB_TCZBActivityLog)) {
            return new CodeModule("TCZBActivityLog"
                    , "tczbactivitylog"
                    , "WL"
                    , "**************************************************************************************************"
                    , "TCZBActivityLog"
                    , "z0s0wCMHs2NPMH7eGP0gcr"
                    , NamingStrategy.no_change
                    , "");
        } else if (name.trim().equals("TCZBTravelAround")) {
            return new CodeModule("TCZBTravelAround"
                    , "around"
                    , ""
                    , "************************************************************************************************************************"
                    , "TCZBTravelAround"
                    , "TXEVDplJPjMQxB9adMJoOx"
                    , NamingStrategy.underline_to_camel
                    , "delete_flag");
        } else if (name.trim().equals("TCZBActivityMarketing")) {
            return new CodeModule("TCZBActivityMarketing"
                    , "marketing"
                    , ""
                    , "*****************************************************************************************************************************"
                    , "TCZBActivityMarketing"
                    , "HsVKkiIJFcm5C5Q8KifKsxhpSV6jGJVK"
                    , NamingStrategy.underline_to_camel
                    , "delete_flag");
        } else if (name.trim().equals("TCZBActivitySelection")) {
            return new CodeModule("TCZBActivitySelection"
                    , "tczbactivityselection"
                    , ""
                    , "*****************************************************************************************************************************"
                    , "TCZBActivitySelection"
                    , "JOINGYupZADpIP7wWjKLK1pH1Qad3C5g"
                    , NamingStrategy.underline_to_camel
                    , "delete_flag");
        } else if (name.trim().equals(DB_TCZBActivityStatistic)) {
            return new CodeModule("TCZBActivityLog"
                    , "tczbactivitystatistic"
                    , ""
                    , "*****************************************************************************************************************************"
                    , "TCZBAStatistic"
                    , "2W2pG1pd1Srk1bytcRZ5Ysh"
                    , NamingStrategy.underline_to_camel
                    , "delete_flag");
        } else if (name.trim().equals(DB_TCZBActivityChat)) {
            return new CodeModule("TCZBActivityChat"
                    , "tczbactivitychat"
                    , ""
                    , "************************************************************************************************************************"
                    , "TCZBActivityChat"
                    , "VzDriev9JHY6Sbmzvt7L"
                    , NamingStrategy.underline_to_camel
                    , "delete_flag");
        } else if (name.trim().equals(DB_TCZBActivityOrder)) {
            return new CodeModule("DB_TCZBActivityOrder"
                    , "tczbactivityorder"
                    , ""
                    , "****************************************************************************************************"
                    , "TCZBActOrder"
                    , "6wDdYrSgWesIr5DaECN4"
                    , NamingStrategy.underline_to_camel
                    , "delete_flag");
        } else if (name.trim().equals(DB_TCSceneryCompetitiveResource)) {
            return new CodeModule("DB_TCSceneryCompetitiveResource"
                    , "tcscenerycompetitiveresource"
                    , ""
                    , "*************************************************************************************************************************"
                    , "TCResource"
                    , "9D6CC92A21ED0C053CC431A46693617529E324534A4FEE859E0A3D0BB87F540D"
                    , NamingStrategy.underline_to_camel
                    , "delete_flag");
        } else if (name.trim().equals(DB_TCZBYResource)){
            return new CodeModule("DB_TCZBYResource",
                    "tczbyresource",
                    "",
                    "*************************************************************************************************",
                    "TCZBYResource_local",
                    "E5MRwNL0ZNYa1ttTXucnBQ72z",
                    NamingStrategy.underline_to_camel,
                    ""
            );
        }
        return null;
    }

    /**
     * 批量替换前缀
     *
     * @param value 值
     * @return {@link String}
     */
    public static String replaceTablePrefix(String value) {
        String text = value;


        for (String searchString : tablePrefixList) {
            if (value.startsWith(searchString)) {
                text = value.replaceFirst(searchString, "");
                break;
            }
        }
        return text;
    }

    /**
     * 替换列前缀(保留第一个大写字母)、首字母小写
     *
     * @param s s
     * @return {@link String}
     */
    public static String replaceColumnPrefixAndFirstTiLower(String s) {
        if (s == null) {
            return null;
        }
        //去除表缩写前缀(保留开头最后一个大写字母)
        Integer firstLowCaseIndex = -1;
        for (int i = 0; i < s.length(); i++) {
            char c = s.charAt(i);
            if (Character.isLowerCase(c)) {
                firstLowCaseIndex = i;
                break;
            }
        }
        if (firstLowCaseIndex > 0) {
            s = StringUtils.uncapitalize(s.substring(firstLowCaseIndex - 1));
        }
        if (s.indexOf(SEPARATOR) == -1) {
            return s;
        }
        s = s.toLowerCase();
        StringBuilder sb = new StringBuilder(s.length());
        boolean upperCase = false;
        for (int i = 0; i < s.length(); i++) {
            char c = s.charAt(i);

            if (c == SEPARATOR) {
                upperCase = true;
            } else if (upperCase) {
                sb.append(Character.toUpperCase(c));
                upperCase = false;
            } else {
                sb.append(c);
            }
        }
        return sb.toString();
    }
}
