package com.ly.ticketfun.etl.domain.templateResource.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.ticketfun.etl.common.enums.templateResource.BookConfirmEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 预订确认
 *
 * <AUTHOR>
 * @date 2025/09/02
 */
@Data
public class BookConfirmDto implements Serializable {
    private static final long serialVersionUID = -7614967459667019018L;
    /**
     * 确认一级模式
     */
    @EnumField(enumClazz = BookConfirmEnum.ConfirmFirstMode.class)
    private String confirmFirstMode;
    /**
     * 确认二级模式
     */
    @EnumField(enumClazz = BookConfirmEnum.ConfirmSecondMode.class)
    private String confirmSecondMode;
    /**
     * 确认三级模式
     */
    @EnumField(enumClazz = BookConfirmEnum.ConfirmThirdMode.class)
    private String confirmThirdMode;
    /**
     * 确认时长类型
     */
    @EnumField(enumClazz = BookConfirmEnum.ConfirmDurationType.class)
    private String confirmDurationType;
    /**
     * 确认分钟数
     */
    private Integer confirmMinutes;

    /**
     * 确认时间分段
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @Data
    public class ConfirmTimeLayerDto implements Serializable {
        private static final long serialVersionUID = 5162163321120378361L;
        /**
         * 比较时间类型
         */
        @EnumField(enumClazz = BookConfirmEnum.CompareTimeType.class)
        private String compareTimeType;
        /**
         * 比较天数
         */
        private Integer compareDays;
        /**
         * 比较小时
         */
        private Integer compareHour;
        /**
         * 比较分钟
         */
        private Integer compareMinute;
        /**
         * 确认时间类型
         */
        @EnumField(enumClazz = BookConfirmEnum.ConfirmTimeType.class)
        private String confirmTimeType;
        /**
         * 确认分钟数
         */
        private String confirmMinutes;
    }
}
