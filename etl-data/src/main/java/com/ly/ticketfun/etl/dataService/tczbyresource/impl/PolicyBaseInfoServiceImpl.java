package com.ly.ticketfun.etl.dataService.tczbyresource.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import com.ly.ticketfun.etl.dataService.tczbyresource.IPolicyBaseInfoService;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyBaseInfo;
import com.ly.ticketfun.etl.mapper.tczbyresource.PolicyBaseInfoMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 政策基础表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-10
 */
@Service
public class PolicyBaseInfoServiceImpl extends DbBaseServiceImpl<PolicyBaseInfoMapper, PolicyBaseInfo> implements IPolicyBaseInfoService {

    @Resource
    private PolicyBaseInfoMapper policyBaseInfoMapper;

    @Override
    public PolicyBaseInfo queryByResourceIdAndPolicyId(Long resourceId, Long policyId) {
        LambdaQueryWrapper<PolicyBaseInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PolicyBaseInfo::getResourceId, resourceId);
        queryWrapper.in(PolicyBaseInfo::getPolicyId, policyId);

        return policyBaseInfoMapper.selectOne(queryWrapper);
    }
}
