package com.ly.ticketfun.etl.dataService.tczbyresource;

import com.ly.localactivity.framework.service.IDbBaseService;
import com.ly.ticketfun.etl.domain.tczbyresource.ResourceBaseInfo;

/**
 * <p>
 * 资源表，分片键：资源ID 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
public interface IResourceBaseInfoService extends IDbBaseService<ResourceBaseInfo> {

    /**
     * 查询景区信息
     *
     * @param resourceId 景区id
     * @return 景区基础信息
     */
    ResourceBaseInfo queryByResourceId(Long resourceId);

}
