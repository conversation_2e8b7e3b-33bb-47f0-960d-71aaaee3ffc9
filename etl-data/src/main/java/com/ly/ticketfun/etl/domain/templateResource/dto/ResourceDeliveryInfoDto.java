package com.ly.ticketfun.etl.domain.templateResource.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.ticketfun.etl.common.enums.templateResource.ResourceDeliveryInfoEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 资源配送信息
 *
 * <AUTHOR>
 * @date 2025/09/01
 */
@Data
public class ResourceDeliveryInfoDto implements Serializable {
    private static final long serialVersionUID = 9220176542305890738L;
    /**
     * 配送类型
     */
    @EnumField(enumClazz = ResourceDeliveryInfoEnum.DeliveryType.class)
    private String deliveryType;
    /**
     * 配送时间类型
     */
    @EnumField(enumClazz = ResourceDeliveryInfoEnum.DeliveryTimeType.class)
    private String deliveryTimeNode;
    /**
     * 配送时间天
     */
    private Integer deliveryTimeDays;
    /**
     * 配送时间小时
     */
    private Integer deliveryTimeHour;
    /**
     * 配送时间分钟
     */
    private Integer deliveryTimeMinute;
}
