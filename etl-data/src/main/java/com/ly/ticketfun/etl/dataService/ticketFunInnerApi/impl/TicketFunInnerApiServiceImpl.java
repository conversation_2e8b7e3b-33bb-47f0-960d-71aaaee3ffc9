package com.ly.ticketfun.etl.dataService.ticketFunInnerApi.impl;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.ly.localactivity.framework.utils.RedisUtils;
import com.ly.localactivity.framework.utils.http.HttpSendUtils;
import com.ly.localactivity.model.domain.tczbactivityresource.agg.MainResourceAgg;
import com.ly.ticketfun.etl.dataService.ticketFunInnerApi.ITicketFunInnerApiService;
import com.ly.ticketfun.etl.dataService.ticketFunInnerApi.properties.TicketFunInnerApiProperties;
import com.ly.ticketfun.etl.domain.common.GlobalRegionDateDto;
import com.ly.ticketfun.etl.domain.common.GlobalRegionSearchResultDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 门票玩乐内部api服务实施
 *
 * <AUTHOR>
 * @date 2025/08/29
 */
@Service
public class TicketFunInnerApiServiceImpl implements ITicketFunInnerApiService {
    @Resource
    private TicketFunInnerApiProperties ticketFunInnerApiProperties;

    @Resource
    private RedisUtils redisUtils;

    /**
     * 查询玩乐主资源汇总
     *
     * @param mainResourceSerialId 主资源Id
     * @param needPackage          需要套餐
     * @param needBookInfo         需要预订信息
     * @return {@link MainResourceAgg}
     */
    public MainResourceAgg queryFunMainResourceAgg(String mainResourceSerialId, Boolean needPackage, Boolean needBookInfo) {
        return new MainResourceAgg();
    }

    /**
     * 查询玩乐主资源汇总
     *
     * @param mainResourceSerialId 主资源Id
     * @param subResourceSerialId  子资源id
     * @param needBookInfo         需要预订信息
     * @return {@link MainResourceAgg}
     */
    public MainResourceAgg queryFunSubResourceAgg(String mainResourceSerialId, String subResourceSerialId, Boolean needBookInfo) {
        return new MainResourceAgg();
    }

    /**
     * 查询玩乐主资源汇总
     *
     * @param mainResourceSerialId 主资源Id
     * @param subResourceSerialId  子资源id
     * @param skuResourceSerialId  sku资源id
     * @param needPrice            需要价格
     * @return {@link MainResourceAgg}
     */
    public MainResourceAgg queryFunSkuResourceAgg(String mainResourceSerialId, String subResourceSerialId, String skuResourceSerialId, Boolean needPrice) {
        return new MainResourceAgg();
    }

    @Override
    public GlobalRegionDateDto globalRegionSearch(Integer countryId,Integer cityId) {
        if (Objects.equals(countryId,1)){
            GlobalRegionDateDto globalRegionDateDto = new GlobalRegionDateDto();
            globalRegionDateDto.setTimeZone("8");
            return globalRegionDateDto;
        }
        String cacheKey = "ticket:globalRegionSearch:" + cityId;
        String cacheValue = redisUtils.get(cacheKey);
        if (StringUtils.isNotEmpty(cacheValue)){
            return JSON.parseObject(cacheValue, new TypeReference<GlobalRegionDateDto>() {
            });
        }

        String url = String.format(
                ticketFunInnerApiProperties.getGlobalRegionUrl() + "&pageIndex=1&pageSize=100&ids=%d&return=id,name,pid,nameShort,path,timeZone,timeZoneType",
                cityId
        );

        GlobalRegionSearchResultDto result = HttpSendUtils
                .create()
                .withUrl(url)
                .doGet(new TypeReference<GlobalRegionSearchResultDto>() {});
        if (result != null && result.getIsSuccess() && !CollectionUtils.isEmpty(result.getResult())){
            redisUtils.setex(cacheKey, JSON.toJSONString(result.getResult().get(0)), RedisUtils.ONE_DAY);
            return result.getResult().get(0);
        }
        redisUtils.setex(cacheKey,"{}",30);
        return null;
    }
}
