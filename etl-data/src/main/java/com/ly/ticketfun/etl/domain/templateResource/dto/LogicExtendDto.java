package com.ly.ticketfun.etl.domain.templateResource.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.ticketfun.etl.common.enums.templateResource.LogicExtendEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 逻辑扩展
 * <p>
 * 慎重！！！
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
@Data
@NoArgsConstructor
public class LogicExtendDto implements Serializable {
    private static final long serialVersionUID = 999550174627002246L;
    /**
     * key
     */
    @EnumField(enumClazz = LogicExtendEnum.key.class)
    private String key;
    /**
     * 值1
     */
    private String value1;

    public LogicExtendDto(String key, Object value1) {
        this.key = key;
        this.value1 = String.valueOf(value1);
    }
}
