package com.ly.ticketfun.etl.dataService.tczbyresource.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import com.ly.ticketfun.etl.dataService.tczbyresource.IResourceBaseInfoService;
import com.ly.ticketfun.etl.domain.tczbyresource.ResourceBaseInfo;
import com.ly.ticketfun.etl.mapper.tczbyresource.ResourceBaseInfoMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 资源表，分片键：资源ID 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Service
public class ResourceBaseInfoServiceImpl extends DbBaseServiceImpl<ResourceBaseInfoMapper, ResourceBaseInfo> implements IResourceBaseInfoService {

    @Resource
    private ResourceBaseInfoMapper resourceBaseInfoMapper;

    @Override
    public ResourceBaseInfo queryByResourceId(Long resourceId) {
        LambdaQueryWrapper<ResourceBaseInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ResourceBaseInfo::getId, resourceId);

        return resourceBaseInfoMapper.selectOne(queryWrapper);
    }
}
