package com.ly.ticketfun.etl.domain.common;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class GlobalRegionSearchResultDto {
    @JSONField(name = "isSuccess")
    private Boolean isSuccess;

    private String message;

    private String serverIP;

    private Integer time;

    private Integer total;

    private List<GlobalRegionDateDto> result;

}
