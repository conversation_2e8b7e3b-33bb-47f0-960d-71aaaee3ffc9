package com.ly.ticketfun.etl.domain.templateResource.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.ticketfun.etl.common.enums.base.CurrencyEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.SkuResourceEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * sku价格
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
@Data
public class SkuResourcePriceDto implements Serializable {
    private static final long serialVersionUID = 3273200234177457489L;

    /**
     * 资源id
     */
    private String resourceId;

    /**
     * 产品id
     */
    private String productId;
    /**
     * 套餐id
     */
    private String packageId;
    /**
     * 资源id
     */
    private String skuId;
    /**
     * 结算价币种
     */
    @EnumField(enumClazz = CurrencyEnum.class)
    private String netPriceCurrency;
    /**
     * 卖价币种
     */
    @EnumField(enumClazz = CurrencyEnum.class)
    private String salePriceCurrency;
    /**
     * 门市价币种
     */
    @EnumField(enumClazz = CurrencyEnum.class)
    private String marketPriceCurrency;
    /**
     * 价格列表
     */
    private List<PriceCalendarDto> priceList;

    /**
     * 价格日历
     *
     * <AUTHOR>
     * @date 2025/09/03
     */
    @Data
    public static class PriceCalendarDto implements Serializable {
        private static final long serialVersionUID = -1475708721141794031L;
        /**
         * 价格日期
         */
        private Long priceDate;
        /**
         * 卖价
         */
        private BigDecimal salePrice;
        /**
         * 结算价
         */
        private BigDecimal netPrice;
        /**
         * 门市价
         */
        private BigDecimal marketPrice;
        /**
         * 库存类型
         */
        @EnumField(enumClazz = SkuResourceEnum.StockType.class)
        private String stockType;
        /**
         * 库存数量
         */
        private Integer stockQuantity;
    }
}
