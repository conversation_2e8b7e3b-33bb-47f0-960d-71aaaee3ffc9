package com.ly.ticketfun.etl.domain.tczbyresource;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 政策预定模式
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PolicyOrderMode implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "DbrGuid", type = IdType.AUTO)
    private Long dbrguid;

    /**
     * 资源id
     */
    private Long resourceId;

    /**
     * 政策id
     */
    private Long policyId;

    /**
     * 是否指定日期
     */
    private Integer ifAppointDate;

    /**
     * 使用时限类型	0当日	1指定日期起	2下单日期起	3指定有效期
     */
    private Integer usePeriodType;

    /**
     * 有效期天、年数
     */
    private Integer validDays;

    /**
     * 有效期天、年数单位：0天1年
     */
    private Integer validDaysUnit;

    /**
     * 指定有效期的开始时间
     */
    private LocalDateTime validityPeriodStart;

    /**
     * 指定有效期的结束时间
     */
    private LocalDateTime validityPeriodEnd;

    /**
     * 是否日期限制0无1可用日期2不可用日期
     */
    private Integer ifDateLimit;

    /**
     * 日期限制：周。数组结构。1周一...7周日。
     */
    private String dateLimitWeek;

    /**
     * 日期限制：日期。数组结构。
     */
    private String dateLimitDate;

    /**
     * 使用次数限制0不限制大于0限制n次
     */
    private Integer useLimitTimes;

    /**
     * 创建人
     */
    @TableField("CreateUser")
    private String createuser;

    /**
     * 创建时间
     */
    @TableField("CreateTime")
    private LocalDateTime createtime;

    /**
     * 更新人
     */
    @TableField("UpdateUser")
    private String updateuser;

    /**
     * 更新时间
     */
    @TableField("UpdateTime")
    private LocalDateTime updatetime;

    /**
     * 是否有效
     */
    private Integer rowStatus;

    /**
     * 入园限制类型 1游玩日期后不可入园 2游玩日期后可入园
     */
    private Integer enterLimitType;


}
