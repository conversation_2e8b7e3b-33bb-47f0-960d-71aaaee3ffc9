package com.ly.ticketfun.etl.domain.templateResource.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.ticketfun.etl.common.enums.templateResource.ResourceSurchargeEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 资源附加费用
 *
 * <AUTHOR>
 * @date 2025/09/02
 */
@Data
public class ResourceSurchargeDto implements Serializable {
    private static final long serialVersionUID = -6549447518670135867L;
    /**
     * 费用类型
     */
    @EnumField(enumClazz = ResourceSurchargeEnum.FeeType.class)
    private String feeType;
    /**
     * 收取方式
     */
    @EnumField(enumClazz = ResourceSurchargeEnum.CollectType.class)
    private String collectType;
    /**
     * 收取途径
     */
    @EnumField(enumClazz = ResourceSurchargeEnum.CollectWay.class)
    private String collectWay;
    /**
     * 承担方
     */
    @EnumField(enumClazz = ResourceSurchargeEnum.CostBear.class)
    private String costBear;
    /**
     * 费用是否可退
     */
    @EnumField(enumClazz = ResourceSurchargeEnum.RefundType.class)
    private String refundType;
    /**
     * 币种
     */
    private String currency;
    /**
     * 金额
     */
    private BigDecimal amount;
}
