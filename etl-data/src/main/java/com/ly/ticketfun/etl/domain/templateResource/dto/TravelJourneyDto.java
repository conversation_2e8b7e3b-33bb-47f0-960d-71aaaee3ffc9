package com.ly.ticketfun.etl.domain.templateResource.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 行程
 *
 * <AUTHOR>
 * @date 2025/09/01
 */
@Data
public class TravelJourneyDto implements Serializable {
    private static final long serialVersionUID = -1165585683410703380L;
    /**
     * 行程id
     */
    private Long journeyId;
    /**
     * 行程名称
     */
    private String journeyName;
    /**
     * 行程天数
     */
    private Integer journeyDays;
    /**
     * 出发城市id
     */
    private Long departureCityId;
    /**
     * 出发城市名称
     */
    private String departureCityName;
    /**
     * 线路明细
     *
     * <AUTHOR>
     * @date 2025/09/01
     */
    @Data
    public static class TravelJourneyDetailDto implements Serializable {
        private static final long serialVersionUID = -697295136729558406L;
        /**
         * 一级类型
         */
        private Integer firstType;
        /**
         * 一级类型描述
         */
        private String firstTypeDesc;
        /**
         * 二级类型
         */
        private Integer secondType;
        /**
         * 二级类型描述
         */
        private String secondTypeDesc;
        /**
         * 三级类型
         */
        private Integer thirdType;
        /**
         * 三级类型描述
         */
        private String thirdTypeDesc;
        /**
         * 详细信息列表
         */
        private List<String> detailInfoList;
        /**
         * 开始时间
         */
        private String startTime;
        /**
         * 活动小时数
         */
        private Integer activityHours;
        /**
         * 活动分钟数
         */
        private Integer activityMinutes;
    }
}
