package com.ly.ticketfun.etl.dataService.tczbyresource.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.ticketfun.etl.dataService.tczbyresource.IPolicyProductRelationService;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyProductRelation;
import com.ly.ticketfun.etl.mapper.tczbyresource.PolicyProductRelationMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 产品货架关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-10
 */
@Service
public class PolicyProductRelationServiceImpl extends DbBaseServiceImpl<PolicyProductRelationMapper, PolicyProductRelation> implements IPolicyProductRelationService {

    @Resource
    private PolicyProductRelationMapper policyProductRelationMapper;

    @Override
    public List<PolicyProductRelation> queryListByPolicyId(Long resourceId, Long policyId) {
        LambdaQueryWrapper<PolicyProductRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PolicyProductRelation::getResourceId, resourceId);
        queryWrapper.eq(PolicyProductRelation::getPolicyId, policyId);
        queryWrapper.eq(PolicyProductRelation::getRowStatus, DataFlagEnum.VALID);
        queryWrapper.gt(PolicyProductRelation::getTagGroupUk, 0);

        return policyProductRelationMapper.selectList(queryWrapper);
    }
}
