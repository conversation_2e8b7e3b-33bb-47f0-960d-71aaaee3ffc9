package com.ly.ticketfun.etl.domain.tczbyresource;

import com.baomidou.mybatisplus.annotation.*;
import com.ly.localactivity.framework.annotation.model.CreateTimeField;
import com.ly.localactivity.framework.annotation.model.ModifyTimeField;
import com.ly.localactivity.framework.annotation.model.TableExtend;
import com.ly.localactivity.framework.annotation.model.TableFieldExtend;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <p>
 * 产品货架关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("policy_product_relation")
@TableExtend(desc = "产品货架关系表")
public class PolicyProductRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "DbrGuid", type = IdType.AUTO)
    @TableFieldExtend(desc = "")
    private Long dbrGuid;

    /**
     * 唯一id
     */
    @TableField("id")
    @TableFieldExtend(desc = "唯一id")
    private Long id;

    /**
     * 景区id
     */
    @TableField("resource_id")
    @TableFieldExtend(desc = "景区id")
    private Long resourceId;

    /**
     * 产品id
     */
    @TableField("product_id")
    @TableFieldExtend(desc = "产品id")
    private Long productId;

    /**
     * 政策id
     */
    @TableField("policy_id")
    @TableFieldExtend(desc = "政策id")
    private Long policyId;

    /**
     * 供应商id
     */
    @TableField("supplier_id")
    @TableFieldExtend(desc = "供应商id")
    private Long supplierId;

    /**
     * 属性UK
     */
    @TableField("tag_group_uk")
    @TableFieldExtend(desc = "属性UK")
    private Long tagGroupUk;

    /**
     * 人群分类主键id
     */
    @TableField("crowd_relation_id")
    @TableFieldExtend(desc = "人群分类主键id")
    private Long crowdRelationId;

    /**
     * 人群分类 (数据字典 603)
     */
    @TableField("crowd_category")
    @TableFieldExtend(desc = "人群分类 (数据字典 603)")
    private Integer crowdCategory;

    /**
     * 人群分类名称
     */
    @TableField("crowd_category_name")
    @TableFieldExtend(desc = "人群分类名称")
    private String crowdCategoryName;

    /**
     * 人群分类对外名称
     */
    @TableField("crowd_category_outer_name")
    @TableFieldExtend(desc = "人群分类对外名称")
    private String crowdCategoryOuterName;

    /**
     * 有效性 1 有效 0 无效
     */
    @TableField("row_status")
    @TableFieldExtend(desc = "有效性 1 有效 0 无效")
    private Integer rowStatus;

    /**
     * 创建时间
     */
    @TableField(value = "CreateTime", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "创建时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField("CreateUser")
    @TableFieldExtend(desc = "创建人")
    private String createUser;

    /**
     * 更新时间
     */
    @TableField(value = "UpdateTime", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "更新时间")
    @ModifyTimeField
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField("UpdateUser")
    @TableFieldExtend(desc = "更新人")
    private String updateUser;


}
