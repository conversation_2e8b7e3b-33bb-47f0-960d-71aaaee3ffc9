package com.ly.ticketfun.etl.domain.tczbyresource;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 出游人基础信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PolicyTravellerBase implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "DbrGuid", type = IdType.AUTO)
    private Long dbrguid;

    /**
     * 资源id
     */
    private Long resourceId;

    /**
     * 政策长id
     */
    private Long policyId;

    /**
     * 是否需要游客信息：2全部 1一位 0否
     */
    private Integer needAllInfo;

    /**
     * 是否需要证件 1需要 0不需要
     */
    private Integer requireIdentification;

    /**
     * 游客基础信息类型- 1姓名 2国内手机号 3国外手机号，多个','号隔开
     */
    private String travellerBaseTypes;

    /**
     * 证件类型Ids 多个','号隔开 60502开头数据字典
     */
    private String identificationTypes;

    /**
     * 包含人群 0全部 1只需要1个
     */
    private Integer needAllCrowd;

    /**
     * 是否有附加信息 1是 0否
     */
    private Integer hasExtendInfo;

    /**
     * 联系人信息 多个','号隔开 1邮箱 2国内手机号 3国外手机号
     */
    private String contactInformation;

    /**
     * 有效性 1有效0无效
     */
    private Integer rowStatus;

    /**
     * 创建时间
     */
    @TableField("CreateTime")
    private LocalDateTime createtime;

    /**
     * 创建人
     */
    @TableField("CreateUser")
    private String createuser;

    /**
     * 修改时间
     */
    @TableField("UpdateTime")
    private LocalDateTime updatetime;

    /**
     * 修改人
     */
    @TableField("UpdateUser")
    private String updateuser;


}
