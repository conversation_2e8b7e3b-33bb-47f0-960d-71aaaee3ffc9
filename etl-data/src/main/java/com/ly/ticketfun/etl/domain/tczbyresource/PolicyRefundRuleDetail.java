package com.ly.ticketfun.etl.domain.tczbyresource;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 政策退改规则明细
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PolicyRefundRuleDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * gid
     */
    @TableId(value = "DbrGuid", type = IdType.AUTO)
    private Long dbrguid;

    /**
     * 资源id
     */
    private Long resourceId;

    /**
     * 政策id
     */
    private Long policyId;

    /**
     * policy_refund_rule表id
     */
    private Long refundRuleId;

    /**
     * 0退1改
     */
    private Integer type;

    /**
     * 0无限制
     * 1游玩日期前
     * 2游玩日期当天
     * 3游玩日期后
     * 4下单日期当天
     * 5下单日期后
     * 6场次开始时间前
     * 7场次开始时间后
     * 8场次结束时间前
     * 9场次结束时间后
     * 10指定日期
     */
    private Integer dateType;

    /**
     * 天数
     */
    private Integer days;

    /**
     * 时间
     */
    private LocalDateTime times;

    /**
     * 分钟
     */
    private Integer minutes;

    /**
     * 日期
     */
    private LocalDateTime date;

    /**
     * 罚金
     */
    private BigDecimal penalty;

    /**
     * 罚金类型0每票1每单
     */
    private Integer penaltyType;

    /**
     * 0币种1百分比
     */
    private Integer penaltyUnit;

    /**
     * 具体币种
     */
    private String currency;

    /**
     * 0订单支付价格1门市价
     */
    private Integer priceType;

    /**
     * 0游玩日期	1出游人身份证	2游玩日期+出游人身份证
     */
    private Integer changeDimension;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 0无效1有效
     */
    private Integer rowStatus;

    /**
     * 创建人
     */
    @TableField("CreateUser")
    private String createuser;

    /**
     * 创建时间
     */
    @TableField("CreateTime")
    private LocalDateTime createtime;

    /**
     * 更新人
     */
    @TableField("UpdateUser")
    private String updateuser;

    /**
     * 更新时间
     */
    @TableField("UpdateTime")
    private LocalDateTime updatetime;


}
