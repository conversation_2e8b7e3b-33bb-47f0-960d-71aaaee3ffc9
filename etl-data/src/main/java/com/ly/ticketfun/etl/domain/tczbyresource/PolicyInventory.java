package com.ly.ticketfun.etl.domain.tczbyresource;

import com.baomidou.mybatisplus.annotation.*;
import com.ly.localactivity.framework.annotation.model.CreateTimeField;
import com.ly.localactivity.framework.annotation.model.ModifyTimeField;
import com.ly.localactivity.framework.annotation.model.TableExtend;
import com.ly.localactivity.framework.annotation.model.TableFieldExtend;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <p>
 * 库存表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("policy_inventory")
@TableExtend(desc = "库存表")
public class PolicyInventory implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分片聚合使用
     */
    @TableId(value = "DbrGuid", type = IdType.AUTO)
    @TableFieldExtend(desc = "分片聚合使用")
    private Long dbrGuid;

    /**
     * 主键
     */
    @TableField("id")
    @TableFieldExtend(desc = "主键")
    private Long id;

    /**
     * 资源id
     */
    @TableField("resource_id")
    @TableFieldExtend(desc = "资源id")
    private Long resourceId;

    /**
     * 日期类型 1游玩日期 2下单日期
     */
    @TableField("date_type")
    @TableFieldExtend(desc = "日期类型 1游玩日期 2下单日期")
    private Integer dateType;

    /**
     * 库存类型 0不限制库存 1总库存 2日库存
     */
    @TableField("type")
    @TableFieldExtend(desc = "库存类型 0不限制库存 1总库存 2日库存")
    private Integer type;

    /**
     * 库存数量
     */
    @TableField("count")
    @TableFieldExtend(desc = "库存数量")
    private Integer count;

    /**
     * 已使用库存
     */
    @TableField("used_count")
    @TableFieldExtend(desc = "已使用库存")
    private Integer usedCount;

    /**
     * 预警库存
     */
    @TableField("warn_count")
    @TableFieldExtend(desc = "预警库存")
    private Integer warnCount;

    /**
     * 数据有效性 0无效  1有效
     */
    @TableField("row_status")
    @TableFieldExtend(desc = "数据有效性 0无效  1有效")
    private Integer rowStatus;

    /**
     * 创建人
     */
    @TableField("CreateUser")
    @TableFieldExtend(desc = "创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "CreateTime", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "创建时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField("UpdateUser")
    @TableFieldExtend(desc = "更新人")
    private String updateUser;

    /**
     * 更新时间
     */
    @TableField(value = "UpdateTime", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "更新时间")
    @ModifyTimeField
    private LocalDateTime updateTime;

    /**
     * 库存组名称
     */
    @TableField("group_name")
    @TableFieldExtend(desc = "库存组名称")
    private String groupName;

    /**
     * 库存组id
     */
    @TableField("group_id")
    @TableFieldExtend(desc = "库存组id")
    private Long groupId;


}
