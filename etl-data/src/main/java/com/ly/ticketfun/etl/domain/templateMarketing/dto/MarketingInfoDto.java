package com.ly.ticketfun.etl.domain.templateMarketing.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-9-10
 * @note
 */
@Data
public class MarketingInfoDto implements Serializable {
    /**
     * 资源id
     */
    private String resourceId;

    /**
     * 产品id
     */
    private String productId;
    /**
     * 套餐id
     */
    private String packageId;
    /**
     * 资源id
     */
    private String skuId;

    /**
     * 营销详情
     */
    private List<MarketingDetailInfoDto> marketingDetailInfoList;
}
