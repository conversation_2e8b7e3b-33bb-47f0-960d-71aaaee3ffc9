package com.ly.ticketfun.etl.domain.tczbyresource;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 政策针对人群表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PolicyTravellerCrowdInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "DbrGuid", type = IdType.AUTO)
    private Long dbrguid;

    /**
     * 资源id
     */
    private Long resourceId;

    /**
     * 政策id
     */
    private Long policyId;

    /**
     * 人群分类id
     */
    private Integer crowdType;

    /**
     * 人群分类名称
     */
    private String crowdTypeName;

    /**
     * 人数限制
     */
    private Integer num;

    /**
     * 是否有限制条件1有 0无
     */
    private Integer hasLimitConditions;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 有效性 1有效0无效
     */
    private Integer rowStatus;

    /**
     * 创建时间
     */
    @TableField("CreateTime")
    private LocalDateTime createtime;

    /**
     * 创建人
     */
    @TableField("CreateUser")
    private String createuser;

    /**
     * 修改时间
     */
    @TableField("UpdateTime")
    private LocalDateTime updatetime;

    /**
     * 修改人
     */
    @TableField("UpdateUser")
    private String updateuser;


}
