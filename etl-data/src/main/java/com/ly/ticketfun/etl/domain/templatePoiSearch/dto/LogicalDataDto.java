package com.ly.ticketfun.etl.domain.templatePoiSearch.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.ticketfun.etl.common.enums.templatePoiSearch.PoiSearchEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-9-9
 * @note 逻辑数据（数字型）
 */
@Data
public class LogicalDataDto {

    /**
     * 多少个用户浏览过
     */
    private Long pv180;

    /**
     * 多少个用户购买过
     */
    private Long uvOrder365;

    /**
     * 点击7天UV
     */
    private Long clickuv7;

    /**
     * 曝光7天UV
     */
    private Long exposureuv7;

    /**
     * 点击去年同期7天UV
     */
    private Long clickuvYOY7;

    /**
     * 曝光去年同期7天UV
     */
    private Long exposureuvYOY7;

    /**
     * 点击30天UV
     */
    private Long clickuv30;

    /**
     * 曝光30天UV
     */
    private Long exposureuv30;

    /**
     * 30天CVR
     */
    private BigDecimal cvr30;

    /**
     * 去年同期30天CVR
     */
    private BigDecimal cvrYOY30;

    /**
     * 自营平台7天单量
     */
    private Long orderOfficial7;

    /**
     * 自营平台180天单量
     */
    private Long orderOfficial180;

    /**
     * 自营平台365天单量
     */
    private Long orderOfficial365;

    /**
     * 全渠道7天单量
     */
    private Long order7;

    /**
     * 全渠道30天单量
     */
    private Long order30;

    /**
     * 全渠道180天单量
     */
    private Long order180;

    /**
     * 全渠道365天单量
     */
    private Long order365;

    /**
     * 全渠道付款单销售份数
     */
    private Long payUnitCount=0L;

    /**
     * 热度分
     */
    private Long hotnessScore;

    /**
     * 竞对销量
     */
    private Long competitorOrder;

    /**
     * 30 天内订单正负佣金（1：正，0：平，-1：负）
     */
    @EnumField(enumClazz = PoiSearchEnum.CommissionStatusEnum.class)
    private String commissionStatus30;


    /**
     * 点评分
     */
    private String commentScore;

    /**
     * 点评总数
     */
    private String commentCount;

    /**
     * 好评数
     */
    private String commentGoodCount;

    /**
     * 中评数
     */
    private String commentMiddleCount;

    /**
     * 差评数
     */
    private String commentBadCount;

    /**
     * 好评率
     */
    private BigDecimal commentGoodRate;

    /**
     * 差评率（总的）
     */
    private BigDecimal commentBadRate180;

}
