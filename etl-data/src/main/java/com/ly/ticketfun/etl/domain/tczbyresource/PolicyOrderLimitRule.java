package com.ly.ticketfun.etl.domain.tczbyresource;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 政策预定限制规则
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PolicyOrderLimitRule implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "DbrGuid", type = IdType.AUTO)
    private Long dbrguid;

    /**
     * 本表唯一id
     */
    private Long ruleId;

    /**
     * 资源id
     */
    private Long resourceId;

    /**
     * 政策id
     */
    private Long policyId;

    /**
     * 组id
     */
    private Long groupId;

    /**
     * 限购id类型	0下单人设备号	1下单人会员id	2出游人手机号	3出游人证件	4联系人手机号
     */
    private Integer idType;

    /**
     * 限购id类型说明
     */
    private String idTypeName;

    /**
     * 限购日期类型：0游玩日期1下单日期
     */
    private Integer dateType;

    /**
     * 限购天数
     */
    private Integer days;

    /**
     * 限购数量
     */
    private Integer num;

    /**
     * 限购数量单位1张2单
     */
    private Integer unit;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建人
     */
    @TableField("CreateUser")
    private String createuser;

    /**
     * 创建时间
     */
    @TableField("CreateTime")
    private LocalDateTime createtime;

    /**
     * 更新人
     */
    @TableField("UpdateUser")
    private String updateuser;

    /**
     * 更新时间
     */
    @TableField("UpdateTime")
    private LocalDateTime updatetime;

    /**
     * 是否有效
     */
    private Integer rowStatus;


}
