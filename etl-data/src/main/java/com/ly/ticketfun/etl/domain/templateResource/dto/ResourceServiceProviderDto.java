package com.ly.ticketfun.etl.domain.templateResource.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.ticketfun.etl.common.enums.templateResource.ResourceServiceProviderEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 资源服务提供商
 *
 * <AUTHOR>
 * @date 2025/09/02
 */
@Data
public class ResourceServiceProviderDto implements Serializable {
    private static final long serialVersionUID = 57509569018932155L;
    /**
     * 品牌提供方
     */
    @EnumField(enumClazz = ResourceServiceProviderEnum.Provider.class)
    private Integer brandType;

    /**
     * 品牌提供方名称
     */
    private String brandName;

    /**
     * 服务商名称
     */
    private String vendorName;

    /**
     * 售前服务提供方
     */
    @EnumField(enumClazz = ResourceServiceProviderEnum.Provider.class)
    private Integer preServiceProvider;

    /**
     * 售前服务时间
     */
    private String preServiceWorkDay;

    /**
     * 售前服务电话
     */
    private String preServicePhone;

    /**
     * 售后服务提供方(1携程 2服务商)
     */
    @EnumField(enumClazz = ResourceServiceProviderEnum.Provider.class)
    private Integer afterServiceProvider;

    /**
     * 售后服务时间
     */

    private String afterServiceWorkDay;

    /**
     * 售后服务电话
     */
    private String afterServicePhone;

    /**
     * 许可证编号
     */
    private String license;

    /**
     * 营业执照编号
     */
    private String businessLicense;
}
