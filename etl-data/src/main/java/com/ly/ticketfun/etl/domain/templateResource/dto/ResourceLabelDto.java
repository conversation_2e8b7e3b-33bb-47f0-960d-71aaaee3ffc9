package com.ly.ticketfun.etl.domain.templateResource.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.ticketfun.etl.common.enums.base.PlatformEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.ResourceLabelEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 资源标签
 *
 * <AUTHOR>
 * @date 2025/09/01
 */
@Data
public class ResourceLabelDto implements Serializable {
    private static final long serialVersionUID = 1359576667611052946L;
    /**
     * 类型
     */
    @EnumField(enumClazz = ResourceLabelEnum.Type.class)
    private String type;
    /**
     * code
     */
    private String code;
    /**
     * 名称
     */
    private String name;
    /**
     * 标签内容
     */
    private String content;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 展示平台
     */
    @EnumField(enumClazz = PlatformEnum.class)
    private List<String> showPlatformList;
    /**
     * 子列表
     */
    private List<ResourceLabelDto> childList;
}
