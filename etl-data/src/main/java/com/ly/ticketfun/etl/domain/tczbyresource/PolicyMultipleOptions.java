package com.ly.ticketfun.etl.domain.tczbyresource;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 政策多选项关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PolicyMultipleOptions implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "DbrGuid", type = IdType.AUTO)
    private Long dbrguid;

    /**
     * 资源id（分片键）
     */
    private Long resourceId;

    /**
     * 政策id
     */
    private Long policyId;

    /**
     * 关联id
     */
    private Long relationId;

    /**
     * 选项类型
     * 1游客信息证件类型
     * 2游客信息基础信息
     * 3电子凭证
     * 4支持证件
     * 5短信信息8入园凭证
     * 9购票人信息  10销售场景
     */
    private Integer selectTypeId;

    /**
     * 选项名称
     */
    private String selectTypeName;

    /**
     * 选项值
     */
    private Integer selectValue;

    /**
     * 选项值说明
     */
    private String selectValueDes;

    /**
     * 有效性 1有效 0无效
     */
    private Integer rowStatus;

    /**
     * 创建时间
     */
    @TableField("CreateTime")
    private LocalDateTime createtime;

    /**
     * 创建人
     */
    @TableField("CreateUser")
    private String createuser;

    /**
     * 修改时间
     */
    @TableField("UpdateTime")
    private LocalDateTime updatetime;

    /**
     * 修改人
     */
    @TableField("UpdateUser")
    private String updateuser;


}
