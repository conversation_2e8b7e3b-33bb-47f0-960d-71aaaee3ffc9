package com.ly.ticketfun.etl.domain.templateResource.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.ticketfun.etl.common.enums.templateResource.ResourceGiftEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 资源赠品
 *
 * <AUTHOR>
 * @date 2025/09/02
 */
@Data
public class ResourceGiftDto implements Serializable {
    private static final long serialVersionUID = 8998201074940873101L;
    /**
     * 内容
     */
    private String content;

    /**
     * 有效期类型
     */
    @EnumField(enumClazz = ResourceGiftEnum.ValidityTimeType.class)
    private String validityTimeType;

    /**
     * 有效期开始时间
     */
    private Long validityBeginTime;

    /**
     * 有效期结束时间
     */
    private Long validityEndTime;
}
