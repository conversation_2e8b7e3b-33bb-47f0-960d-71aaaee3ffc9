package com.ly.ticketfun.etl.dataService.tczbyresource.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import com.ly.ticketfun.etl.dataService.tczbyresource.IPolicyInventoryService;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyInventory;
import com.ly.ticketfun.etl.mapper.tczbyresource.PolicyInventoryMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 库存表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Service
public class PolicyInventoryServiceImpl extends DbBaseServiceImpl<PolicyInventoryMapper, PolicyInventory> implements IPolicyInventoryService {

    @Resource
    private PolicyInventoryMapper policyInventoryMapper;

    @Override
    public List<PolicyInventory> queryListByIdList(Long resourceId, List<Long> idList) {
        LambdaQueryWrapper<PolicyInventory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PolicyInventory::getResourceId, resourceId);
        queryWrapper.in(PolicyInventory::getId, idList);

        return policyInventoryMapper.selectList(queryWrapper);
    }
}
