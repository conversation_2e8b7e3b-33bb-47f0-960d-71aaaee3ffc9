package com.ly.ticketfun.etl.dataService.tczbyresource;

import com.ly.localactivity.framework.service.IDbBaseService;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyProductRelation;

import java.util.List;

/**
 * <p>
 * 产品货架关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-10
 */
public interface IPolicyProductRelationService extends IDbBaseService<PolicyProductRelation> {

    /**
     * 获取政策挂载货架列表
     *
     * @param resourceId 景区id
     * @param policyId   政策id
     * @return 挂载货架列表
     */
    List<PolicyProductRelation> queryListByPolicyId(Long resourceId, Long policyId);
}
