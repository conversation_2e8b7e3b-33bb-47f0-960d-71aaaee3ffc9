package com.ly.ticketfun.etl.domain.tczbyresource;

import com.baomidou.mybatisplus.annotation.*;
import com.ly.localactivity.framework.annotation.model.CreateTimeField;
import com.ly.localactivity.framework.annotation.model.ModifyTimeField;
import com.ly.localactivity.framework.annotation.model.TableExtend;
import com.ly.localactivity.framework.annotation.model.TableFieldExtend;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <p>
 * 政策基础表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("policy_base_info")
@TableExtend(desc = "政策基础表")
public class PolicyBaseInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "DbrGuid", type = IdType.AUTO)
    @TableFieldExtend(desc = "")
    private Long dbrGuid;

    /**
     * 政策长id
     */
    @TableField("policy_id")
    @TableFieldExtend(desc = "政策长id")
    private Long policyId;

    /**
     * 政策名称
     */
    @TableField("policy_name")
    @TableFieldExtend(desc = "政策名称")
    private String policyName;

    /**
     * 资源id
     */
    @TableField("resource_id")
    @TableFieldExtend(desc = "资源id")
    private Long resourceId;

    /**
     * 政策短id
     */
    @TableField("policy_original_id")
    @TableFieldExtend(desc = "政策短id")
    private Integer policyOriginalId;

    /**
     * 鸟巢原始策略id
     */
    @TableField("original_price_id")
    @TableFieldExtend(desc = "鸟巢原始策略id")
    private Integer originalPriceId;

    /**
     * 协议id
     */
    @TableField("contract_id")
    @TableFieldExtend(desc = "协议id")
    private Long contractId;

    /**
     * 供应商id
     */
    @TableField("supplier_id")
    @TableFieldExtend(desc = "供应商id")
    private Long supplierId;

    /**
     * 政策模式 232数据字典
     */
    @TableField("policy_mode")
    @TableFieldExtend(desc = "政策模式 232数据字典")
    private Integer policyMode;

    /**
     * 是否有外部场次 0 没有 1有
     */
    @TableField("has_outer_session")
    @TableFieldExtend(desc = "是否有外部场次 0 没有 1有")
    private Integer hasOuterSession;

    /**
     * 对接方式 1eb  11合作系统
     */
    @TableField("docking_method")
    @TableFieldExtend(desc = "对接方式 1eb  11合作系统")
    private Integer dockingMethod;

    /**
     * 销售场景 多个用','号隔开 1线上 2线下 3票机 4交叉
     */
    @TableField("sales_scenarios")
    @TableFieldExtend(desc = "销售场景 多个用','号隔开 1线上 2线下 3票机 4交叉")
    private String salesScenarios;

    /**
     * 支付方式0到付 1在线支付
     */
    @TableField("pay_type")
    @TableFieldExtend(desc = "支付方式0到付 1在线支付")
    private Integer payType;

    /**
     * 是否需要指定日期 1需要 2无需
     */
    @TableField("use_day_mode")
    @TableFieldExtend(desc = "是否需要指定日期 1需要 2无需")
    private Integer useDayMode;

    /**
     * 是否实名制 0否 1是
     */
    @TableField("realname")
    @TableFieldExtend(desc = "是否实名制 0否 1是")
    private Integer realname;

    /**
     * 票型合作类型：0票务合作 1招商合作
     */
    @TableField("ticket_cooperation_type")
    @TableFieldExtend(desc = "票型合作类型：0票务合作 1招商合作")
    private Integer ticketCooperationType;

    /**
     * 订单过期自动取消（1开启 0关闭）
     */
    @TableField("order_overdue")
    @TableFieldExtend(desc = "订单过期自动取消（1开启 0关闭）")
    private Integer orderOverdue;

    /**
     * 是否有下单限制 0无限制1有限制
     */
    @TableField("order_limit")
    @TableFieldExtend(desc = "是否有下单限制 0无限制1有限制")
    private Integer orderLimit;

    /**
     * 销售开始日期
     */
    @TableField("sale_begin_time")
    @TableFieldExtend(desc = "销售开始日期")
    private LocalDateTime saleBeginTime;

    /**
     * 销售结束时间
     */
    @TableField("sale_end_time")
    @TableFieldExtend(desc = "销售结束时间")
    private LocalDateTime saleEndTime;

    /**
     * 有效开始时间
     */
    @TableField("effective_start_time")
    @TableFieldExtend(desc = "有效开始时间")
    private LocalDateTime effectiveStartTime;

    /**
     * 有效期结束时间
     */
    @TableField("effective_end_time")
    @TableFieldExtend(desc = "有效期结束时间")
    private LocalDateTime effectiveEndTime;

    /**
     * 政策来源 1:自签 2:携程 4:艺龙 8:Switch 9:开放平台(景区) 10:新开放平台 11:电子导览 12:美团 13:客路
     */
    @TableField("source_from")
    @TableFieldExtend(desc = "政策来源 1:自签 2:携程 4:艺龙 8:Switch 9:开放平台(景区) 10:新开放平台 11:电子导览 12:美团 13:客路")
    private Integer sourceFrom;

    /**
     * 合作方类型 72901自营 72902三方 72903携程 72904无合作 72905自营三方
     */
    @TableField("partner_type_id")
    @TableFieldExtend(desc = "合作方类型 72901自营 72902三方 72903携程 72904无合作 72905自营三方")
    private Integer partnerTypeId;

    /**
     * 特殊类型 0非特殊 25202等值 景酒套餐......
     */
    @TableField("special_type_id")
    @TableFieldExtend(desc = "特殊类型 0非特殊 25202等值 景酒套餐......")
    private Integer specialTypeId;

    /**
     * 屏蔽前台展示 1展示 0不展示
     */
    @TableField("block_display")
    @TableFieldExtend(desc = "屏蔽前台展示 1展示 0不展示")
    private Integer blockDisplay;

    /**
     * 备注信息 (内部标记，不做对客展示)
     */
    @TableField("remark")
    @TableFieldExtend(desc = "备注信息 (内部标记，不做对客展示)")
    private String remark;

    /**
     * 货币编号 CNY人民币
     */
    @TableField("sale_price_currency_code")
    @TableFieldExtend(desc = "货币编号 CNY人民币")
    private String salePriceCurrencyCode;

    /**
     * 结算价货币编号 CNY人民币
     */
    @TableField("balance_price_currency_code")
    @TableFieldExtend(desc = "结算价货币编号 CNY人民币")
    private String balancePriceCurrencyCode;

    /**
     * 上架状态 0待上架 1上架 2下架
     */
    @TableField("listing_status")
    @TableFieldExtend(desc = "上架状态 0待上架 1上架 2下架")
    private Integer listingStatus;

    /**
     * 对账时间类型（0旅游时间 1创建时间 2核单时间 3发货时间）
     */
    @TableField("billing_date_type")
    @TableFieldExtend(desc = "对账时间类型（0旅游时间 1创建时间 2核单时间 3发货时间）")
    private Integer billingDateType;

    /**
     * 订单量 （订单清洗后获得）
     */
    @TableField("order_num")
    @TableFieldExtend(desc = "订单量 （订单清洗后获得）")
    private Integer orderNum;

    /**
     * 无效或下架数据从老库同步新库 1是 0否
     */
    @TableField("old_new_flag")
    @TableFieldExtend(desc = "无效或下架数据从老库同步新库 1是 0否")
    private Integer oldNewFlag;

    /**
     * 有效性 1有效 0无效
     */
    @TableField("row_status")
    @TableFieldExtend(desc = "有效性 1有效 0无效")
    private Integer rowStatus;

    /**
     * 创建时间
     */
    @TableField(value = "CreateTime", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "创建时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField("CreateUser")
    @TableFieldExtend(desc = "创建人")
    private String createUser;

    /**
     * 修改时间
     */
    @TableField(value = "UpdateTime", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "修改时间")
    @ModifyTimeField
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField("UpdateUser")
    @TableFieldExtend(desc = "修改人")
    private String updateUser;


}
