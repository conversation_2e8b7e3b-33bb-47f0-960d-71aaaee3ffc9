package com.ly.ticketfun.etl.domain.tczbyresource;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ly.localactivity.framework.annotation.model.*;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.localactivity.model.enums.common.DeleteFlagEnum;


/**
 * <p>
 * 产品表，分片键：资源ID
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ResourceProduct")
@TableExtend(desc = "产品表，分片键：资源ID")
public class ResourceProduct implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键18位
     */
    @TableField("RPNId")
    @TableFieldExtend(desc = "主键18位")
    private Long id;

    /**
     * 资源ID，分片键
     */
    @TableField("RPResourceId")
    @TableFieldExtend(desc = "资源ID，分片键")
    private Long resourceId;

    /**
     * 票种ID
     */
    @TableField("RPTTIId")
    @TableFieldExtend(desc = "票种ID")
    private Long tTIId;

    /**
     * 对应第三方产品ID
     */
    @TableField("RPOuterProductId")
    @TableFieldExtend(desc = "对应第三方产品ID")
    private String outerProductId;

    /**
     * 资源类型（0酒店1景区）
     */
    @TableField("RPResourceTypeId")
    @TableFieldExtend(desc = "资源类型（0酒店1景区）")
    private Integer resourceTypeId;

    /**
     * 产品名称
     */
    @TableField("RPProductName")
    @TableFieldExtend(desc = "产品名称")
    private String productName;

    /**
     * 运营分类Id(23412)，前台外显
     */
    @TableField("RPProductClass")
    @TableFieldExtend(desc = "运营分类Id(23412)，前台外显")
    private Integer productClass;

    /**
     * 运营分类名称(园内玩乐)，前台外显
     */
    @TableField("RPProductClassName")
    @TableFieldExtend(desc = "运营分类名称(园内玩乐)，前台外显")
    private String productClassName;

    /**
     * 产品单元Id(23501,23502)
     */
    @TableField("RPProductUnit")
    @TableFieldExtend(desc = "产品单元Id(23501,23502)")
    private Integer productUnit;

    /**
     * 产品单元名称(单票，套票)
     */
    @TableField("RPProductUnitName")
    @TableFieldExtend(desc = "产品单元名称(单票，套票)")
    private String productUnitName;

    /**
     * 针对人群603数据字典(60301)
     */
    @TableField("RPConsumers")
    @TableFieldExtend(desc = "针对人群603数据字典(60301)")
    private Integer consumers;

    /**
     * 成人数
     */
    @TableField("RPAdultNum")
    @TableFieldExtend(desc = "成人数")
    private Integer adultNum;

    /**
     * 儿童数
     */
    @TableField("RPChildrenNum")
    @TableFieldExtend(desc = "儿童数")
    private Integer childrenNum;

    /**
     * 老人数
     */
    @TableField("RPTheAgedNum")
    @TableFieldExtend(desc = "老人数")
    private Integer theAgedNum;

    /**
     * 学生数
     */
    @TableField("RPStudentNum")
    @TableFieldExtend(desc = "学生数")
    private Integer studentNum;

    /**
     * 开始年龄：针对单个票种（暂时没有使用）
     */
    @TableField("RPBeginAge")
    @TableFieldExtend(desc = "开始年龄：针对单个票种（暂时没有使用）")
    private Integer beginAge;

    /**
     * 结束年龄：针对单个票种（暂时没有使用）
     */
    @TableField("RPEndAge")
    @TableFieldExtend(desc = "结束年龄：针对单个票种（暂时没有使用）")
    private Integer endAge;

    /**
     * 产品分类Id(29601)
     */
    @TableField("RPProductType")
    @TableFieldExtend(desc = "产品分类Id(29601)")
    private Integer productType;

    /**
     * 产品分类名称
     */
    @TableField("RPProductTypeName")
    @TableFieldExtend(desc = "产品分类名称")
    private String productTypeName;

    /**
     * 房型面积
     */
    @TableField("RPProductSize")
    @TableFieldExtend(desc = "房型面积")
    private String productSize;

    /**
     * 房型楼层
     */
    @TableField("RPProductFloor")
    @TableFieldExtend(desc = "房型楼层")
    private String productFloor;

    /**
     * 是否有效 0无效 1有效
     */
    @TableField("RPRowStatus")
    @TableFieldExtend(desc = "是否有效 0无效 1有效")
    private Integer rowStatus;

    /**
     * 排序值
     */
    @TableField("RPSort")
    @TableFieldExtend(desc = "排序值")
    private Integer sort;

    /**
     * 新增人工号[姓名]
     */
    @TableField("CreateUser")
    @TableFieldExtend(desc = "新增人工号[姓名]")
    private String createUser;

    /**
     * 新增时间
     */
    @TableField(value = "CreateTime", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "新增时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 修改人工号[姓名]
     */
    @TableField("UpdateUser")
    @TableFieldExtend(desc = "修改人工号[姓名]")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField(value = "UpdateTime", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "修改时间")
    @ModifyTimeField
    private LocalDateTime updateTime;

    /**
     * 分片聚合用
     */
    @TableId(value = "DbrGuid", type = IdType.AUTO)
    @TableFieldExtend(desc = "分片聚合用")
    private Long dbrGuid;

    /**
     * 短信门票名称
     */
    @TableField("RPNoteName")
    @TableFieldExtend(desc = "短信门票名称")
    private String noteName;

    /**
     * 产品描述
     */
    @TableField("RPProductDesc")
    @TableFieldExtend(desc = "产品描述")
    private String productDesc;

    /**
     * 票型名称类型 0自定义 1标准类型
     */
    @TableField("RPNoteType")
    @TableFieldExtend(desc = "票型名称类型 0自定义 1标准类型")
    private Integer noteType;

    /**
     * 票型名称类型的大类名称-L3
     */
    @TableField("RPNoteTypeName")
    @TableFieldExtend(desc = "票型名称类型的大类名称-L3")
    private String noteTypeName;

    /**
     * 票型名称类型的小类，json格式
     */
    @TableField("RPNoteTypeDetail")
    @TableFieldExtend(desc = "票型名称类型的小类，json格式")
    private String noteTypeDetail;

    /**
     * 新适用人群（数据字典 233；用英文逗号隔开）
     */
    @TableField("RPApplyCrowd")
    @TableFieldExtend(desc = "新适用人群（数据字典 233；用英文逗号隔开）")
    private String applyCrowd;

    /**
     * 产品所属区域（0 门票产品；1 酒景产品；2 行中产品；3 其它）
     */
    @TableField("RPProductBelongArea")
    @TableFieldExtend(desc = "产品所属区域（0 门票产品；1 酒景产品；2 行中产品；3 其它）")
    private Integer productBelongArea;

    /**
     * 产品所属类型（0 标准产品；1 非标产品；2 活动产品；3 酒景产品；4 电子导览产品；5 其它）
     */
    @TableField("RPProductBelongType")
    @TableFieldExtend(desc = "产品所属类型（0 标准产品；1 非标产品；2 活动产品；3 酒景产品；4 电子导览产品；5 其它）")
    private Integer productBelongType;

    /**
     * 货架产品类型1 景区标准 2同程标准
     */
    @TableField("RPMarketTypeId")
    @TableFieldExtend(desc = "货架产品类型1 景区标准 2同程标准")
    private Integer marketTypeId;

    /**
     * 货架排序值（越大越靠前）
     */
    @TableField("RPMarketSort")
    @TableFieldExtend(desc = "货架排序值（越大越靠前）")
    private Integer marketSort;

    /**
     * 供给标准  1=是  0=否
     */
    @TableField("RPSupplyStatus")
    @TableFieldExtend(desc = "供给标准  1=是  0=否")
    private Integer supplyStatus;

    /**
     * 亮点
     */
    @TableField("RPHighlights")
    @TableFieldExtend(desc = "亮点")
    private String highlights;

    /**
     * 货架主图
     */
    @TableField("RPProductMainImg")
    @TableFieldExtend(desc = "货架主图")
    private String productMainImg;


}
