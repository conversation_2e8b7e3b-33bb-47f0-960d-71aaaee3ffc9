package com.ly.ticketfun.etl.domain.templatePoiSearch.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-9-9
 * @note 热门线路
 */
@Data
public class PopularRoutesDto implements Serializable {
    /**
     * 资源id
     */
    private Long resourceId;

    /**
     * 活动流水号
     */
    private String acticitySerialId;

    /**
     * 活动类型
     */
    private Integer activityType;

    /**
     * 活动标题
     */
    private String acticityTitle;

    /**
     * 活动副标题
     */
    private String acticitySubTitle;

    /**
     * 活动主图
     */
    private String acticityHeadImage;

    /**
     * 出发城市Id
     */
    private Long departureCityId;

    /**
     * 出发城市下活动排序值（倒序）
     */
    private Integer activitySort;

    /**
     * 活动下资源排序（倒序）
     */
    private Integer resourceSort;
}
