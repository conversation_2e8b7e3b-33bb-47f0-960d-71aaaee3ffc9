package com.ly.ticketfun.etl.domain.templateResource.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 条款信息
 *
 * <AUTHOR>
 * @date 2025/09/01
 */
@Data
public class ClauseTypeDto implements Serializable {
    private static final long serialVersionUID = 6965973970461926804L;
    /**
     * 条款类型Code
     */
    private String clauseTypeCode;
    /**
     * 标题
     */
    private String title;
    /**
     * 条款可用数量
     */
    private Integer clauseAvailableCount;
    /**
     * 内容
     */
    private String content;
    /**
     * 条款列表
     */
    private List<ClauseDto> clauseList;

    /**
     * 条款
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @Data
    public static class ClauseDto {
        /**
         * 条款Code
         */
        private String clauseCode;
        /**
         * 条款标题
         */
        private String title;
        /**
         * 条款明细
         */
        private List<ClauseItemDto> clauseItemList;
    }

    /**
     * 条款明细
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @Data
    public static class ClauseItemDto {
        /**
         * 条款明细Code
         */
        private String clauseItemCode;
        /**
         * 标题
         */
        private String title;
        /**
         * 内容
         */
        private String content;
        /**
         * 图标url
         */
        private String iconUrl;
        /**
         * 适用人群Ids,隔开
         */
        private String suitCrowdIds;
        /**
         * 适用人群Code 用,隔开
         */
        private String suitCrowdCodes;
    }
}
