package com.ly.ticketfun.etl.domain.tczbyresource;

import com.baomidou.mybatisplus.annotation.*;
import com.ly.localactivity.framework.annotation.model.CreateTimeField;
import com.ly.localactivity.framework.annotation.model.ModifyTimeField;
import com.ly.localactivity.framework.annotation.model.TableExtend;
import com.ly.localactivity.framework.annotation.model.TableFieldExtend;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * <p>
 * 政策价格日历表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("policy_price_calendar")
@TableExtend(desc = "政策价格日历表")
public class PolicyPriceCalendar implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分片聚合使用
     */
    @TableId(value = "DbrGuid", type = IdType.AUTO)
    @TableFieldExtend(desc = "分片聚合使用")
    private Long dbrGuid;

    /**
     * 主键
     */
    @TableField("id")
    @TableFieldExtend(desc = "主键")
    private Long id;

    /**
     * 资源id
     */
    @TableField("resource_id")
    @TableFieldExtend(desc = "资源id")
    private Long resourceId;

    /**
     * 政策id
     */
    @TableField("policy_id")
    @TableFieldExtend(desc = "政策id")
    private Long policyId;

    /**
     * 日期
     */
    @TableField("travel_date")
    @TableFieldExtend(desc = "日期")
    private LocalDateTime travelDate;

    /**
     * 门市价
     */
    @TableField("amount")
    @TableFieldExtend(desc = "门市价")
    private BigDecimal amount;

    /**
     * 销售价
     */
    @TableField("sale_amount")
    @TableFieldExtend(desc = "销售价")
    private BigDecimal saleAmount;

    /**
     * 结算价
     */
    @TableField("contract_amount")
    @TableFieldExtend(desc = "结算价")
    private BigDecimal contractAmount;

    /**
     * 销售状态 0禁止销售 1销售
     */
    @TableField("sale_status")
    @TableFieldExtend(desc = "销售状态 0禁止销售 1销售")
    private Integer saleStatus;

    /**
     * 库存id（inventory表）
     */
    @TableField("inventory_id")
    @TableFieldExtend(desc = "库存id（inventory表）")
    private Long inventoryId;

    /**
     * 库存展示 0不展示 1展示
     */
    @TableField("stock_show")
    @TableFieldExtend(desc = "库存展示 0不展示 1展示")
    private Integer stockShow;

    /**
     * 数据有效性 0无效  1有效
     */
    @TableField("row_status")
    @TableFieldExtend(desc = "数据有效性 0无效  1有效")
    private Integer rowStatus;

    /**
     * 创建人
     */
    @TableField("CreateUser")
    @TableFieldExtend(desc = "创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "CreateTime", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "创建时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField("UpdateUser")
    @TableFieldExtend(desc = "更新人")
    private String updateUser;

    /**
     * 更新时间
     */
    @TableField(value = "UpdateTime", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "更新时间")
    @ModifyTimeField
    private LocalDateTime updateTime;


}
