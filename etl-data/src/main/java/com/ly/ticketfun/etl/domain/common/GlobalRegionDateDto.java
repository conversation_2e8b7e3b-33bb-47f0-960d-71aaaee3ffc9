package com.ly.ticketfun.etl.domain.common;

import lombok.Data;

@Data
public class GlobalRegionDateDto {
    private String path;

    private String name;

    private String flightThreeCode;

    /**
     * 省份id
     */
    private String pid;

    /**
     * 城市id
     */
    private String id;

    private String nameShort;

    /**
     * 时区类型 1:夏令 2:冬令
     */
    private Integer timeZoneType;

    /**
     * 时区
     */
    private String timeZone;
}
