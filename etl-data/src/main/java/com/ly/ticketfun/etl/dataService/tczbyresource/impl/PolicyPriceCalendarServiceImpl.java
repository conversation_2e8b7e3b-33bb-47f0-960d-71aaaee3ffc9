package com.ly.ticketfun.etl.dataService.tczbyresource.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.ticketfun.etl.dataService.tczbyresource.IPolicyPriceCalendarService;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyPriceCalendar;
import com.ly.ticketfun.etl.mapper.tczbyresource.PolicyPriceCalendarMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 政策价格日历表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Service
public class PolicyPriceCalendarServiceImpl extends DbBaseServiceImpl<PolicyPriceCalendarMapper, PolicyPriceCalendar> implements IPolicyPriceCalendarService {

    @Resource
    private PolicyPriceCalendarMapper policyPriceCalendarMapper;

    @Override
    public List<PolicyPriceCalendar> querySaleListByResourceIdAndPolicyId(Long resourceId,
                                                                          Long policyId,
                                                                          LocalDate beginDate,
                                                                          LocalDate endDate
    ) {
        LambdaQueryWrapper<PolicyPriceCalendar> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PolicyPriceCalendar::getResourceId, resourceId);
        queryWrapper.eq(PolicyPriceCalendar::getPolicyId, policyId);
        queryWrapper.eq(PolicyPriceCalendar::getRowStatus, DataFlagEnum.VALID);
        queryWrapper.eq(PolicyPriceCalendar::getSaleStatus, 1);
        if (beginDate != null) {
            queryWrapper.ge(PolicyPriceCalendar::getTravelDate, beginDate);
        }
        if (beginDate != null) {
            queryWrapper.le(PolicyPriceCalendar::getTravelDate, endDate);
        }

        return policyPriceCalendarMapper.selectList(queryWrapper);
    }
}
