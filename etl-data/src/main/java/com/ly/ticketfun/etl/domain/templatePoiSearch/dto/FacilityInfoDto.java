package com.ly.ticketfun.etl.domain.templatePoiSearch.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.ticketfun.etl.common.enums.templatePoiSearch.PoiSearchEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-9-9
 * @note
 */
@Data
public class FacilityInfoDto implements Serializable {

    /**
     * 设施id
     */
    private String id;

    /**
     * 设施名称
     */
    private String name;

    /**
     * 类型id
     */
    private Integer typeId;

    /**
     * 类型名称
     */
    private String typeName;

    /**
     * 使用月
     */
    private String useMonth;

    /**
     * 显示图片
     */
    private String showUrl;

    /**
     * 来源,0人工,1标签清洗job
     */
    @EnumField(enumClazz = PoiSearchEnum.FacilitySourceFromEnum.class)
    private String facilitySourceFrom;

}
