package com.ly.ticketfun.etl.domain.tczbyresource;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 政策核销规则
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PolicyUseRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 表id
     */
    @TableId(value = "DbrGuid", type = IdType.AUTO)
    private Long dbrguid;

    /**
     * 资源id
     */
    private Long resourceId;

    /**
     * 政策id
     */
    private Long policyId;

    /**
     * 是否实名0否1是
     */
    private Integer ifRealName;

    /**
     * 入园方式0直接入园1换票2自定义
     */
    private Integer enterType;

    /**
     * 是否需要所有凭证0否1是
     */
    private Integer voucherAll;

    /**
     * 补充说明
     */
    private String supplementDesc;

    /**
     * 入园换票地址时间是否相同
     */
    private Integer ifSameAddress;

    /**
     * 入园联系电话
     */
    private String enterParkTel;

    /**
     * 入园次数
     */
    private Integer enterParkTimes;

    /**
     * json数组：入园快捷设备 0 手持机 1自助取票机
     */
    private String enterParkQuickDevice;

    /**
     * 是否有效0否1是
     */
    private Integer rowStatus;

    /**
     * 创建人
     */
    @TableField("CreateUser")
    private String createuser;

    /**
     * 创建时间
     */
    @TableField("CreateTime")
    private LocalDateTime createtime;

    /**
     * 更新人
     */
    @TableField("UpdateUser")
    private String updateuser;

    /**
     * 更新时间
     */
    @TableField("UpdateTime")
    private LocalDateTime updatetime;


}
