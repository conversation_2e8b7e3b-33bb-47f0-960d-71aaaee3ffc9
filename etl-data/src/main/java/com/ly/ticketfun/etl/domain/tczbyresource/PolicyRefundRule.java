package com.ly.ticketfun.etl.domain.tczbyresource;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 退改规则
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PolicyRefundRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 表id
     */
    @TableId(value = "DbrGuid", type = IdType.AUTO)
    private Long dbrguid;

    /**
     * 资源id
     */
    private Long resourceId;

    /**
     * 政策长id
     */
    private Long policyId;

    /**
     * 退款类型：0不可退 1随时退 2条件退
     */
    private Integer refundType;

    /**
     * 当日是否可退0否1是
     */
    private Integer todayRefund;

    /**
     * 是否过期自动退：0否1是
     */
    private Integer ifOverdueAutoRefund;

    /**
     * 是否支持部分退：0不支持1按出游人2按二维码
     */
    private Integer partialRefund;

    /**
     * 0不支持改1条件改
     */
    private Integer changeType;

    /**
     * 是有有效0否1是
     */
    private Integer rowStatus;

    /**
     * 创建人
     */
    @TableField("CreateUser")
    private String createuser;

    /**
     * 创建时间
     */
    @TableField("CreateTime")
    private LocalDateTime createtime;

    /**
     * 更新人
     */
    @TableField("UpdateUser")
    private String updateuser;

    /**
     * 更新时间
     */
    @TableField("UpdateTime")
    private LocalDateTime updatetime;


}
