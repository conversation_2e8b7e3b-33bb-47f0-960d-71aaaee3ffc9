package com.ly.ticketfun.etl.dataService.tczbyresource;

import com.ly.localactivity.framework.service.IDbBaseService;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyPriceCalendar;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 政策价格日历表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
public interface IPolicyPriceCalendarService extends IDbBaseService<PolicyPriceCalendar> {

    /**
     * 查询销售日历列表
     *
     * @param resourceId 景区id
     * @param policyId   政策id
     * @param beginDate  开始日期
     * @param endDate    结束日期
     * @return 政策价格日历列表
     */
    List<PolicyPriceCalendar> querySaleListByResourceIdAndPolicyId(Long resourceId,
                                                                   Long policyId,
                                                                   LocalDate beginDate,
                                                                   LocalDate endDate
    );
}
