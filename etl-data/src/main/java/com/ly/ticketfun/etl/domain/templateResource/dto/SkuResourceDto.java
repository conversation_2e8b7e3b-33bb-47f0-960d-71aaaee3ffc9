package com.ly.ticketfun.etl.domain.templateResource.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.tcbase.i18n.tran.annotation.Tran;
import com.ly.ticketfun.etl.common.enums.templateResource.ProductResourceEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.SkuResourceEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * sku资源
 *
 * <AUTHOR>
 * @date 2025/09/01
 */
@Data
public class SkuResourceDto implements Serializable {
    private static final long serialVersionUID = 5475309711641745231L;
    /**
     * 产品id
     */
    private String productId;
    /**
     * 套餐id
     */
    private String packageId;
    /**
     * 资源id
     */
    private String skuId;
    /**
     * 名称
     */
    @Tran
    private String name;
    /**
     * 单位人数，每份资源适用的人数
     */
    private Integer unitQuantity;

    /**
     * 单位人数；每份资源适用的最大人数
     */
    private Integer unitMaxQuantity;
    /**
     * 预订最小份数
     */
    private Integer bookMinQuantity;
    /**
     * 预订最大份数
     */
    private Integer bookMaxQuantity;
    /**
     * 资源分类（人群）
     */
    private List<ResourceBandInfoDto> bandInfo;
    /**
     * 单独售卖类型
     */
    @EnumField(enumClazz = SkuResourceEnum.SaleAloneType.class)
    private String saleAloneType;
    /**
     * 售卖状态
     */
    @EnumField(enumClazz = ProductResourceEnum.SaleStatus.class)
    private String saleStatus;
}
