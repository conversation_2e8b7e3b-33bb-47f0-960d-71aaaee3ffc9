package com.ly.ticketfun.etl.dataService.tczbyresource.impl;

import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import com.ly.ticketfun.etl.dataService.tczbyresource.IPolicyRefundRuleService;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyRefundRule;
import com.ly.ticketfun.etl.mapper.tczbyresource.PolicyRefundRuleMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 退改规则 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Service
public class PolicyRefundRuleServiceImpl extends DbBaseServiceImpl<PolicyRefundRuleMapper, PolicyRefundRule> implements IPolicyRefundRuleService {

}
