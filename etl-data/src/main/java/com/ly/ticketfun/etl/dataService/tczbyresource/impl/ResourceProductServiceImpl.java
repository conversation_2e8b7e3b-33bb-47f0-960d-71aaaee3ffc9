package com.ly.ticketfun.etl.dataService.tczbyresource.impl;

import com.ly.ticketfun.etl.domain.tczbyresource.ResourceProduct;
import com.ly.ticketfun.etl.mapper.tczbyresource.ResourceProductMapper;
import com.ly.ticketfun.etl.dataService.tczbyresource.IResourceProductService;
import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 产品表，分片键：资源ID 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Service
public class ResourceProductServiceImpl extends DbBaseServiceImpl<ResourceProductMapper, ResourceProduct> implements IResourceProductService {

}
