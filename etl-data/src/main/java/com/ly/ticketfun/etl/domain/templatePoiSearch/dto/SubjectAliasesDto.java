package com.ly.ticketfun.etl.domain.templatePoiSearch.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.ticketfun.etl.common.enums.templatePoiSearch.PoiSearchEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-9-9
 * @note 别名主题
 */
@Data
public class SubjectAliasesDto implements Serializable {

    /**
     * 资源主键ID
     */
    private Long resourceId;

    /**
     * 资源类型 (0：自身，1：主景点，2：子景点，3：关联景区)
     */
    @EnumField(enumClazz = PoiSearchEnum.SubjectTypeEnum.class)
    private String type;

    /**
     * 关联资源ID
     */
    private Long travelResourceId;

    /**
     * 关联资源当地语言名称
     */
    private String travelResourceLocalName;

    /**
     * 关联资源英文名称
     */
    private String travelResourceEnglishName;

    /**
     * 关联资源一级主题ID
     */
    private Long travelResourceThemeId;

    /**
     * 关联资源一级主题名称
     */
    private String travelResourceThemeName;

    /**
     * 关联资源二级主题ID
     */
    private Long travelResourceSubThemeId;

    /**
     * 关联资源二级主题名称
     */
    private String travelResourceSubThemeName;

    /**
     * 关联资源别名
     * 格式：[{"name":"灵山梵宫","orderNum":13},{"name":"灵山圣境","orderNum":3},{"name":"灵山大佛","orderNum":2}]
     */
    private String travelResourceAliasName;

}
