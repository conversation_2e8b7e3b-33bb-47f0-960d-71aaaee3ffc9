package com.ly.ticketfun.etl.domain.templateResource.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.ticketfun.etl.common.enums.templateResource.BookQuotaLimitEnum;
import com.ly.ticketfun.etl.common.enums.ticket.UnitEnums;
import lombok.Data;

import java.io.Serializable;

/**
 * 购买配额限制
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
@Data
public class BookQuotaLimitDto implements Serializable {
    private static final long serialVersionUID = -1234641741303611309L;
    /**
     * 分组id
     */
    private String groupId;
    /**
     * 限制目标类型
     */
    @EnumField(enumClazz = BookQuotaLimitEnum.LimitTargetType.class)
    private String limitTargetType;
    /**
     * 限制目标描述
     */
    private String limitTargetDesc;
    /**
     * 限制日期类型
     */
    @EnumField(enumClazz = BookQuotaLimitEnum.LimitDateType.class)
    private String limitDateType;
    /**
     * 限制周期天数
     */
    private String limitPeriodDays;
    /**
     * 配额数量
     */
    private String quotaQuantity;
    /**
     * 配额单位
     */
    @EnumField(enumClazz = UnitEnums.class)
    private String quotaUnit;
}
