package com.ly.ticketfun.etl.domain.tczbyresource;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 针对人群限制表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PolicyTravellerCrowdLimit implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "DbrGuid", type = IdType.AUTO)
    private Long dbrguid;

    /**
     * 资源id
     */
    private Long resourceId;

    /**
     * 政策id
     */
    private Long policyId;

    /**
     * 人群分类表主键id
     */
    private Long crowdInfoId;

    /**
     * 限制条件组id
     */
    private Long groupId;

    /**
     * 限制类型0 年龄 1性别 2特殊区域 3出生日期 4生肖 5姓名 6 学生
     */
    private Integer limitType;

    /**
     * 限制类型名称
     */
    private String limitTypeName;

    /**
     * 验证规则 1所选类型可购票 2所选类型不可购票
     */
    private Integer canBook;

    /**
     * 维度类型 	101年龄年 102年龄月 103 年龄天		301省份维度302城市维度303区县		501出生日年，502出生日月，503出生日日
     */
    private Integer dimensionType;

    /**
     * 限制内容( json格式)
     */
    private String limitConditions;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 有效性 1有效0无效
     */
    private Integer rowStatus;

    /**
     * 创建时间
     */
    @TableField("CreateTime")
    private LocalDateTime createtime;

    /**
     * 创建人
     */
    @TableField("CreateUser")
    private String createuser;

    /**
     * 修改时间
     */
    @TableField("UpdateTime")
    private LocalDateTime updatetime;

    /**
     * 修改人
     */
    @TableField("UpdateUser")
    private String updateuser;


}
