package com.ly.ticketfun.etl.dataService.tczbyresource;

import com.ly.localactivity.framework.service.IDbBaseService;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyInventory;

import java.util.List;

/**
 * <p>
 * 库存表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
public interface IPolicyInventoryService extends IDbBaseService<PolicyInventory> {

    /**
     * 查询库存列表
     *
     * @param resourceId 景区id
     * @param idList     库存id列表
     * @return 库存列表
     */
    List<PolicyInventory> queryListByIdList(Long resourceId, List<Long> idList);
}
