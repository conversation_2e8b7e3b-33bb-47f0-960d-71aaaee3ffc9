package com.ly.ticketfun.etl.dataService.tczbyresource.impl;

import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import com.ly.ticketfun.etl.dataService.tczbyresource.IPolicyRefundRuleDetailService;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyRefundRuleDetail;
import com.ly.ticketfun.etl.mapper.tczbyresource.PolicyRefundRuleDetailMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 政策退改规则明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Service
public class PolicyRefundRuleDetailServiceImpl extends DbBaseServiceImpl<PolicyRefundRuleDetailMapper, PolicyRefundRuleDetail> implements IPolicyRefundRuleDetailService {

}
