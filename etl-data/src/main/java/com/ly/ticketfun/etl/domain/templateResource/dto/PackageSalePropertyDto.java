package com.ly.ticketfun.etl.domain.templateResource.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 套餐销售属性
 *
 * <AUTHOR>
 * @date 2025/09/01
 */
@Data
public class PackageSalePropertyDto implements Serializable {
    private static final long serialVersionUID = -7065001489522254698L;
    /**
     * 属性id
     */
    private Long propertyId;
    /**
     * 属性Code
     */
    private String propertyCode;
    /**
     * 属性名称
     */
    private String propertyName;
    /**
     * 属性值id
     */
    private String
            propertyValueId;
    /**
     * 属性值名称
     */
    private String propertyValueName;
    /**
     * 属性组id
     */
    private String propertyGroupId;
    /**
     * 子属性
     */
    private List<PackageSalePropertyDto> subProperties;
}
