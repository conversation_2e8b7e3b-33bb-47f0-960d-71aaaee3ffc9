package com.ly.ticketfun.etl.domain.templateResource.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 销售渠道
 *
 * <AUTHOR>
 * @date 2025/09/01
 */
@Data
public class SaleChannelDto implements Serializable {
    private static final long serialVersionUID = 8207497206057459009L;
    /**
     * 销售终端Code
     */
    private String salePointCode;
    /**
     * 销售终端名称
     */
    private String salePointName;
    /**
     * 销售渠道id
     */
    private String saleChannelId;
    /**
     * 销售渠道名称
     */
    private String saleChannelName;
    /**
     * 销售RefId
     */
    private String saleRefId;

    /**
     * 资源id
     */
    private String resourceId;

    /**
     * 产品id
     */
    private String productId;
    /**
     * 套餐id
     */
    private String packageId;
    /**
     * 人民币卖价
     */
    private BigDecimal salePrice_CNY;
    /**
     * 人民币结算价
     */
    private BigDecimal netPrice_CNY;
    /**
     * 人民币门市价
     */
    private BigDecimal marketPrice_CNY;

    /**
     * 是否有售卖时间限制
     */
    @EnumField(enumClazz = WhetherEnum.class)
    private Integer saleTimeLimit;
    /**
     * 售卖开始时间
     */
    private Long saleBeginTime;
    /**
     * 售卖结束时间
     */
    private Long saleEndTime;

}
