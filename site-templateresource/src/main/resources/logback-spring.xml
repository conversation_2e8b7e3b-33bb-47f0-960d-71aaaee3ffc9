<configuration>
    <springProperty scope="context" name="logPath"  source="logging.file.path"/>
    <!--  LOG-FILE Appender  -->
    <appender name="TCSCENERY.JAVA.TICKETFUN.TEMPLATERESOURCE.ETL-LOG-FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>
            ${logPath}/logs/skynet-tcscenery.java.ticketfun.templateresource.etl/app/tcscenery.java.ticketfun.templateresource.etl.log
        </file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>
                ${logPath}/skynet-tcscenery.java.ticketfun.templateresource.etl/skynet_archived/tcscenery.java.ticketfun.templateresource.etl.%d{yyyy-MM-dd}.log
            </fileNamePattern>
            <maxHistory>6</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %X{apmTrace} %-5level %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!--  STDOUT Appender  -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread] %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!--  Checklist Appender  -->
    <appender name="checklistAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--日志文件输出的文件名-->
            <FileNamePattern>
                ${logPath}/skynet-tcscenery.java.ticketfun.templateresource.etl/checklist/checklist.%d{yyyy-MM-dd}.log
            </FileNamePattern>
            <MaxHistory>10</MaxHistory>
        </rollingPolicy>
        <encoder>
            <charset>UTF-8</charset>
            <pattern>%msg%n</pattern>
        </encoder>
    </appender>
    <!-- Checklist Logger  -->
    <logger name="checklistLogEntityLogger" level="INFO" additivity="false">
        <appender-ref ref="checklistAppender"/>
    </logger>

    <!--  日志输出  -->
    <springProfile name="dev">
        <root level="INFO" >
            <!--  测试使用 不要推送到线上环境  -->
            <appender-ref ref="STDOUT" />
            <appender-ref ref="TCSCENERY.JAVA.TICKETFUN.TEMPLATERESOURCE.ETL-LOG-FILE"/>
        </root>
    </springProfile>
    <springProfile name="!dev">
        <root level="INFO" >
            <appender-ref ref="TCSCENERY.JAVA.TICKETFUN.TEMPLATERESOURCE.ETL-LOG-FILE"/>
        </root>
    </springProfile>

    <logger name="com.ly.dal" level="ERROR"/>
    <logger name="com.ly.spat.dsf.monitor" level="ERROR"/>
    <logger name="com.ly.spat.dsf.client" level="ERROR"/>
    <logger name="com.ly.spat.dsf.utils.NetUtils" level="ERROR"/>
    <logger name="RocketmqClient" level="ERROR"/>
</configuration>
