package com.ly.ticketfun.etl.common.enums.ticket;

/**
 * 游客基础信息类型- 1姓名 2国内手机号 3国外手机号
 *
 * <AUTHOR> @date 2024-5-16
 * @note
 */
public enum TravellerBaseInfoTypeEnums {
    NAME(1, "姓名"),
    CHINA_MOBILE(2, "国内手机号"),
    FOREIGN_MOBILE(3, "国外手机号");

    TravellerBaseInfoTypeEnums(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    final Integer code;
    final String message;

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static String getName(int code) {
        for (TravellerBaseInfoTypeEnums c : TravellerBaseInfoTypeEnums.values()) {
            if (c.getCode() == code) {
                return c.message;
            }
        }
        return null;
    }

}
