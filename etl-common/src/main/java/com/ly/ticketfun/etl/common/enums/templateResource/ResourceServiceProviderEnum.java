package com.ly.ticketfun.etl.common.enums.templateResource;

import com.ly.localactivity.model.enums.tczbactivityresource.MainResourceServiceProviderEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ResourceServiceProviderEnum
 *
 * <AUTHOR>
 * @date 2025/9/10
 */
public interface ResourceServiceProviderEnum {

    @AllArgsConstructor
    @Getter
    enum Provider {
        CTRIP(1, "携程", MainResourceServiceProviderEnum.Provider.CTRIP.getValue()),
        SERVICE_PROVIDER(2, "服务商", MainResourceServiceProviderEnum.Provider.SERVICE_PROVIDER.getValue());

        private final Integer value;
        private final String name;
        private final Integer funValue;
    }
}
