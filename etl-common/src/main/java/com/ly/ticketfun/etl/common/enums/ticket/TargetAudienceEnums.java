package com.ly.ticketfun.etl.common.enums.ticket;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-9-10
 * @note
 */
@AllArgsConstructor
@Getter
public enum TargetAudienceEnums {
    ADULT("23302", "成人"),
    STUDENT_FULL_TIME("23303", "全日制大中小学生"),
    CHILD("23304", "儿童"),
    ELDER("23305", "老人"),
    STUDENT_FULL_TIME_EXCLUDE_ADULT("23308", "全日制大中小学生（不含成人教育、研究生）"),
    DISABLED("23309", "残疾人"),
    TEACHER("23310", "教师"),
    JOURNALIST("23311", "记者"),
    ACTIVE_SERVICE_MILITARY("23312", "现役军人"),
    VETERAN("23313", "退役军人"),
    RETIRED_CADRE("23314", "离休干部"),
    THREE_FAMILY_MEMBERS("23315", "三属人员"),
    MEDICAL_STAFF("23316", "医护人员"),
    LOCAL_RESIDENT("23317", "本地市民"),
    FOREIGNER("23318", "外籍人士"),
    LABOR_MODEL("23319", "劳动模范"),
    GROUP_TOUR_GUIDE("23320", "跟团导游"),
    RELIGIOUS_PERSON("23321", "宗教人士"),
    TOUR_GUIDE("23322", "导游"),
    OTHER("23323", "其他"),
    MINOR("23324", "未成年"),
    COLLEGE_STUDENT("23325", "大学生"),
    PRIMARY_MIDDLE_HIGH_SCHOOL_STUDENT("23326", "小中高学生"),
    FIRE_RESCUE_TEAM_MEMBER("23327", "国家综合性消防救援队员"),
    BLOOD_DONATION_HONOR_HOLDER("23328", "省内无偿献血、无偿捐献造血干细胞及无偿献血终生荣誉奖个人"),
    UNLIMITED("23329", "不限人群");

    private final String value;
    private final String name;
}
