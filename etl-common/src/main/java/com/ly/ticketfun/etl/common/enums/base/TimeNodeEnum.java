package com.ly.ticketfun.etl.common.enums.base;

import com.ly.localactivity.model.enums.tczbactivityresource.SubResourceTakeReturnEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单时间节点枚举
 *
 * <AUTHOR>
 * @date 2025/09/02
 */
@AllArgsConstructor
@Getter
public enum TimeNodeEnum {
    CREATE_DATE("CREATE_DATE", "下单日期"),

    USAGE_DATE("USAGE_DATE", "出游(使用)日期"),
    BEFORE_USAGE_DATE("BEFORE_USAGE_DATE", "出游(使用)日期前"),
    AFTER_PAY_TIME("AFTER_PAY_TIME", "支付时间后"),

    AFTER_ORDER_CONFIRM("AFTER_ORDER_CONFIRM", "订单确认后"),

    SPECIFY_DATE("SPECIFY_DATE", "指定日期"),
    ;

    private final String value;
    private final String name;

    /**
     * 来自fun enum
     *
     * @param latestDeliveryTimeNode  最新交货时间节点
     * @param latestDeliveryTimeAfter 之后最新交货时间
     * @return {@link TimeNodeEnum }
     */
    public static TimeNodeEnum fromFunEnum(SubResourceTakeReturnEnum.LatestDeliveryTimeNode latestDeliveryTimeNode, SubResourceTakeReturnEnum.LatestDeliveryTimeAfter latestDeliveryTimeAfter) {
        if (SubResourceTakeReturnEnum.LatestDeliveryTimeNode.ORDER_CONFIRM.equals(latestDeliveryTimeNode)) {
            if (SubResourceTakeReturnEnum.LatestDeliveryTimeAfter.AFTER.equals(latestDeliveryTimeAfter)) {
                return AFTER_ORDER_CONFIRM;
            } else {
                return AFTER_PAY_TIME;
            }
        } else if (SubResourceTakeReturnEnum.LatestDeliveryTimeNode.USE_DATE.equals(latestDeliveryTimeNode)) {
            if (SubResourceTakeReturnEnum.LatestDeliveryTimeAfter.BEFORE.equals(latestDeliveryTimeAfter)) {
                return BEFORE_USAGE_DATE;
            } else {
                return USAGE_DATE;
            }
        }
        return null;
    }
}
