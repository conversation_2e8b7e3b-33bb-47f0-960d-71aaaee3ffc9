package com.ly.ticketfun.etl.common.enums.templateResource;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 产品逻辑扩展枚举
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
public interface LogicExtendEnum {
    /**
     * key
     *
     * <AUTHOR>
     * @date 2025/09/03
     */
    @AllArgsConstructor
    @Getter
    enum key {
        ORDER_CREATE_VOLUME("ORDER_CREATE_VOLUME", "创单量"),
        ORDER_CREATE_VOLUME_15D("ORDER_CREATE_VOLUME_15D", "近15天创单量"),
        ORDER_CREATE_VOLUME_30D("ORDER_CREATE_VOLUME_30D", "近30天创单量"),
        ORDER_CREATE_VOLUME_180D("ORDER_CREATE_VOLUME_180D", "近180天创单量"),

        ORDER_PAY_VOLUME_15D("ORDER_PAY_VOLUME_15D", "近15天付款单量"),
        ORDER_PAY_VOLUME_30D("ORDER_PAY_VOLUME_30D", "近30天付款单量"),
        ORDER_PAY_VOLUME_180D("ORDER_PAY_VOLUME_180D", "近180天付款单量"),

        ORDER_PAY_UNIT_VOLUME("ORDER_PAY_UNIT_VOLUME", "付款单销售份数"),

        COMMENT_SCORE("COMMENT_SCORE", "点评分"),
        COMMENT_COUNT("COMMENT_COUNT", "点评总数"),
        COMMENT_GOOD_COUNT("COMMENT_GOOD_COUNT", "好评数"),
        COMMENT_MIDDLE_COUNT("COMMENT_MIDDLE_COUNT", "中评数"),
        COMMENT_BAD_COUNT("COMMENT_BAD_COUNT", "差评数"),
        COMMENT_SUMMARY("COMMENT_SUMMARY", "点评AI一句话"),

        /**
         * 0内部场次 1外部场次 2520222 多场次标识 2520226纯期票联票
         */
        Package_Special_Sign("Package_Special_Sign","套餐特殊标识"),


        /**
         * 是否存在问卷
         */
        HAS_QUESTIONNAIRE("hasQuestionnaire", "是否存在问卷"),


        /**
         * 取件方式
         */
        TAKE_TYPE("takeType", "取件方式"),
        /**
         * 自取城市
         */
        SELF_PICKUP_CITY("selfPickupCity", "自取城市"),
        /**
         * 发货城市
         */
        DELIVERY_CITY("deliveryCity", "发货城市"),
        /**
         * 待机时长
         */
        STANDBY_DURATION("standbyDuration", "待机时长"),
        ;
        private final String value;
        private final String name;
    }
}
