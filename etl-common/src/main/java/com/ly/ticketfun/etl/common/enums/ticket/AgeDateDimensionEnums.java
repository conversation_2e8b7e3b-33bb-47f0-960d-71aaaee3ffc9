package com.ly.ticketfun.etl.common.enums.ticket;

/**
 * 年龄日期维度
 */
public enum AgeDateDimensionEnums {
    Age(101, "年龄年"),
    AgeMonth(102, "年龄月"),
    AgeDay(103, "年龄日");

    AgeDateDimensionEnums(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    final Integer code;
    final String message;

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
