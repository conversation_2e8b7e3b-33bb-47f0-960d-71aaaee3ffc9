package com.ly.ticketfun.etl.common.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 模版商品库转换异常
 *
 * <AUTHOR>
 * @date 2025/09/09
 */
@Getter
public class TRTransformException extends RuntimeException {
    TRTransformException.ErrorInfo errorInfo;

    String[] extendList;

    public TRTransformException(TRTransformException.ErrorInfo errorInfo, String... extendList) {
        super(errorInfo.getMsg());
        this.errorInfo = errorInfo;
        this.extendList = extendList;
    }

    @AllArgsConstructor
    @Getter
    public enum ErrorInfo {
        PRODUCT_TRANSFORM_FAIL("PRODUCT_TRANSFORM_FAIL", "产品转换失败,productId:{0},model:{1}"),
        PACKAGE_TRANSFORM_FAIL("PACKAGE_TRANSFORM_FAIL", "套餐转换失败,productId:{0},packageId:{1},model:{2}"),
        SKU_TRANSFORM_FAIL("PACKAGE_TRANSFORM_FAIL", "SKU转换失败,poiId:{0},packageId:{1},model:{2}"),

        PRODUCT_ONLINE_FAIL("PRODUCT_ONLINE_FAIL", "产品上架失败"),
        PACKAGE_ONLINE_FAIL("PACKAGE_ONLINE_FAIL", "资源上架失败"),
        SKU_ONLINE_FAIL("SKU_ONLINE_FAIL", "SKU上架失败"),
        PRODUCT_OFFLINE_FAIL("PRODUCT_OFFLINE_FAIL", "产品下架失败"),
        PACKAGE_OFFLINE_FAIL("PACKAGE_OFFLINE_FAIL", "资源下架失败"),
        SKU_OFFLINE_FAIL("SKU_OFFLINE_FAIL", "SKU下架失败"),

        REQ_PARAMS_ERROR("REQ_PARAMS_ERROR", "入参错误"),
        RESOURCE_NOT_EXISTS("RESOURCE_NOT_EXISTS", "资源不存在"),
        RESOURCE_NOT_ON_SALE("RESOURCE_INVALID", "资源不在售"),
        ENUM_NOT_MAPPING("ENUM_NOT_MAPPING", "枚举映射错误，key:{0}，value:{1}"),
        ;
        private final String code;
        private final String msg;
    }
}
