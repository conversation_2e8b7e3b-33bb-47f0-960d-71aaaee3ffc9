package com.ly.ticketfun.etl.common.enums.ticket;

/**
 * 性别 101 男性 102 女性
 */
public enum TravellerValueGenderEnums {
    Male(101, "男性"),
    Female(102, "女性");

    TravellerValueGenderEnums(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    final Integer code;
    final String message;

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
