package com.ly.ticketfun.etl.common.enums.ticket;

/**
 * 证件类型Ids 多个','号隔开 60502开头数据字典
 * 6050201 身份证
 * 6050202 护照
 * 6050203 台胞证
 * 6050204 港澳通行证
 * 6050209 回乡证
 * 6050210 永久居留身份证
 */
public enum TravellerIdCardTypeEnums {
    Id_Card(6050201, "身份证"),
    Passport(6050202, "护照"),
    <PERSON><PERSON><PERSON>(6050203, "台胞证"),
    <PERSON><PERSON><PERSON><PERSON>(6050204, "港澳通行证"),
    <PERSON><PERSON><PERSON><PERSON>(6050209, "回乡证"),
    <PERSON><PERSON><PERSON>(6050210, "永久居留身份证");

    TravellerIdCardTypeEnums(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    final Integer code;
    final String message;

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static String getName(int code) {
        for (TravellerIdCardTypeEnums c : TravellerIdCardTypeEnums.values()) {
            if (c.getCode() == code) {
                return c.message;
            }
        }
        return null;
    }

}
