package com.ly.ticketfun.etl.common.enums.ticket;

/**
 * 是否需要游客信息：2全部 1一位 0否
 *
 * <AUTHOR> @date 2024-5-16
 * @note
 */
public enum TravellerNeedTypeEnums {
    ALL(2, "全部"),
    One(1, "一位"),
    UN_NEED(0, "否");

    TravellerNeedTypeEnums(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    final Integer code;
    final String message;

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static String getName(int code) {
        for (TravellerNeedTypeEnums c : TravellerNeedTypeEnums.values()) {
            if (c.getCode() == code) {
                return c.message;
            }
        }
        return null;
    }

}
