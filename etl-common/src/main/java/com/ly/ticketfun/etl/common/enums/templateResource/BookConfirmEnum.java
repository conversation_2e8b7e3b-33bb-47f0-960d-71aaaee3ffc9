package com.ly.ticketfun.etl.common.enums.templateResource;

import com.ly.localactivity.model.enums.tczbactivityresource.MainResourceBookConfigEnum;
import com.ly.ticketfun.etl.common.enums.base.TimeNodeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 预订确认枚举
 *
 * <AUTHOR>
 * @date 2025/09/02
 */
public interface BookConfirmEnum {
    /**
     * 确认一级模式
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum ConfirmFirstMode {
        EBK_CONFIRM("EBK_CONFIRM", "EBK确认", MainResourceBookConfigEnum.ConfirmFirstMode.EBK.getValue()),
        ;

        private final String value;
        private final String name;
        private final Integer funValue;
    }

    /**
     * 确认二级模式
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum ConfirmSecondMode {
        AUTO_CONFIRM("AUTO_CONFIRM", "人工确认", MainResourceBookConfigEnum.ConfirmSecondMode.AUTO.getValue()),
        ARTIFICIAL_CONFIRM("ARTIFICIAL_CONFIRM", "自动确认", MainResourceBookConfigEnum.ConfirmSecondMode.ARTIFICIAL.getValue()),
        ;

        private final String value;
        private final String name;
        private final Integer funValue;
    }

    /**
     * 确认三级模式
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum ConfirmThirdMode {
        STOCK_ORDER_AUTO_CONFIRM("STOCK_ORDER_AUTO_CONFIRM", "库存订单自动确认", MainResourceBookConfigEnum.ConfirmThirdMode.STOCK_ORDER_AUTO_CONFIRM.getValue()),
        ;

        private final String value;
        private final String name;
        private final Integer funValue;
    }

    /**
     * 认时长类型
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum ConfirmDurationType {
        CONSTANT("CONSTANT", "固定时长", MainResourceBookConfigEnum.ConfirmDurationType.FIXED_DURATION.getValue()),
        LAYER("LAYER", "分段时长", MainResourceBookConfigEnum.ConfirmDurationType.SEGMENTED_DURATION.getValue()),
        ;

        private final String value;
        private final String name;
        private final Integer funValue;
    }

    /**
     * 分段时间类型
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum CompareTimeType {
        BEFORE_TRAVEL_DATE(TimeNodeEnum.BEFORE_USAGE_DATE),
        ;

        CompareTimeType(TimeNodeEnum usageDate) {
            this.value = usageDate.getValue();
            this.name = usageDate.getName();
        }

        private final String value;
        private final String name;
    }

    /**
     * 比较时间类型
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum ConfirmTimeType {
        BEFORE_TRAVEL_DATE(TimeNodeEnum.BEFORE_USAGE_DATE),
        AFTER_PAY_TIME(TimeNodeEnum.AFTER_PAY_TIME),
        ;

        ConfirmTimeType(TimeNodeEnum usageDate) {
            this.value = usageDate.getValue();
            this.name = usageDate.getName();
        }

        private final String value;
        private final String name;

    }
}
