package com.ly.ticketfun.etl.common.enums.ticket;

/**
 * 购票人信息
 * 1邮箱
 * 2大陆手机号
 * 3非大陆手机号
 *
 * <AUTHOR>
 * @date 2024-5-16
 * @note
 */
public enum TravellerLinkerInfoTypeEnums {

    /**
     * 购票人信息
     * * 1邮箱
     * * 2大陆手机号
     * * 3非大陆手机号
     */
    EMAIL(1, "邮箱"),
    MAINLAND(2, "大陆手机号"),
    NOT_MAINLAND(3, "非大陆手机号"),
    NAME(4, "姓名");

    private final Integer code;
    private final String message;

    TravellerLinkerInfoTypeEnums(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

}
