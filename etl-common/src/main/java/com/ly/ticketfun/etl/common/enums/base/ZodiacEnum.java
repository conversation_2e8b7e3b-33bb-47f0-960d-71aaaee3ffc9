package com.ly.ticketfun.etl.common.enums.base;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 生肖枚举
 *
 * <AUTHOR>
 * @date 2025/09/05
 */
@AllArgsConstructor
@Getter
public enum ZodiacEnum {
    RAT("鼠", "Rat", 201),
    OX("牛", "Ox", 202),
    TIGER("虎", "Tiger", 203),
    RABBIT("兔", "Rabbit", 204),
    DRAGON("龙", "Dragon", 205),
    SNAKE("蛇", "Snake", 206),
    HORSE("马", "Horse", 207),
    GOAT("羊", "Goat", 208),
    MONKEY("猴", "Monkey", 209),
    ROOSTER("鸡", "Rooster", 210),
    DOG("狗", "Dog", 211),
    PIG("猪", "Pig", 212);

    private final String chineseName;
    private final String englishName;
    private final Integer ticketValue;

    public static ZodiacEnum fromYear(int year) {
        int index = (year - 4) % 12;
        if (index < 0) {
            index += 12;
        }
        return values()[index];
    }

    public static String getValue(Integer type) {
        ZodiacEnum[] typeEnums = values();
        for (ZodiacEnum typeEnum : typeEnums) {
            if (typeEnum.getTicketValue().equals(type)) {
                return typeEnum.getEnglishName();
            }
        }
        return "";
    }
}
