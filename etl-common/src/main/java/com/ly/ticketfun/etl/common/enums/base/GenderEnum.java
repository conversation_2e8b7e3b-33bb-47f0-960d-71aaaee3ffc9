package com.ly.ticketfun.etl.common.enums.base;

import com.ly.ticketfun.etl.common.enums.ticket.TravellerValueGenderEnums;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 性别枚举
 *
 * <AUTHOR>
 * @date 2025/09/05
 */
@AllArgsConstructor
@Getter
public enum GenderEnum {
    MALE("MALE", "男性", null, TravellerValueGenderEnums.Male.getCode()),
    FEMALE("FEMALE", "女性", null, TravellerValueGenderEnums.Female.getCode()),
    ;
    private final String value;
    private final String name;
    private final String fullValue;
    private final Integer ticketValue;

}
