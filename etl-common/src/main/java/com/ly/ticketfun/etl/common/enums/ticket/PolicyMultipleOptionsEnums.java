package com.ly.ticketfun.etl.common.enums.ticket;

/**
 * 政策多选项关联表中类型枚举
 *
 * <AUTHOR>
 * @date 2024-5-13
 * @note
 */
public enum PolicyMultipleOptionsEnums {
    /**
     * 政策多选项关联表中类型枚举
     */
    TravellerInfoIdType(1, "游客信息证件类型"),
    BasicInformationOfTraveller(2, "游客信息基础信息"),
    ElectronicVouchersType(3, "电子凭证"),
    SupportCertificate(4, "支持证件"),
    MsgType(5, "短信信息"),
    GetTicketUserInfo(6, "取票人信息"),
    DirectEnterParkVouchers(7, "直接入园凭证"),
    Vouchers(8, "入园凭证"),
    ContactUserInfo(9, "购票人信息"),
    SaleScenarios(10, "销售场景");

    private final Integer code;
    private final String message;

    PolicyMultipleOptionsEnums(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
