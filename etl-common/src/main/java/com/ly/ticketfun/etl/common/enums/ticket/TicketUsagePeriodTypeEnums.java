package com.ly.ticketfun.etl.common.enums.ticket;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-9-10
 * @note
 */
@AllArgsConstructor
@Getter
public enum TicketUsagePeriodTypeEnums {
    //使用时限类型 0当日 1指定日期起 2下单日期起 3指定有效期
    DAY(0, "当日"),
    SPECIFY_DATE_START(1, "指定日期起"),
    ORDER_DATE_START(2, "下单日期起"),
    SPECIFY_VALIDITY_PERIOD(3, "指定有效期");
    private final Integer value;
    private final String name;
}
