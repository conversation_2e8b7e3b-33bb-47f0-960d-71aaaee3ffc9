package com.ly.ticketfun.etl.common.enums.ticket;

/**
 * 限购数量单位1张2单
 */
public enum UnitEnums {
    Pie(1, "Pie"),
    Orders(2, "Orders");

    UnitEnums(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    final Integer code;
    final String value;

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getV(int code) {
        for (UnitEnums c : UnitEnums.values()) {
            if (c.getCode() == code) {
                return c.value;
            }
        }
        return null;
    }
}
