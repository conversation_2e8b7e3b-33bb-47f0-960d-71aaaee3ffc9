package com.ly.ticketfun.etl.common.enums.templateResource;

import com.ly.ticketfun.etl.common.enums.base.TimeNodeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 预订配额限制枚举
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
public interface BookQuotaLimitEnum {
    /**
     * 限制目标类型
     *
     * <AUTHOR>
     * @date 2025/09/03
     */
    @AllArgsConstructor
    @Getter
    enum LimitTargetType {
        ORDER_DEVICE_NO("ORDER_DEVICE_NO", "下单人设备号", 0),
        ORDER_MEMBER_ID("ORDER_MEMBER_ID", "下单人会员id", 1),
        PASSENGER_MOBILE("PASSENGER_MOBILE", "出游人手机号", 2),
        PASSENGER_CREDENTIALS("PASSENGER_CREDENTIALS", "出游人证件", 3),
        CONTACT_MOBILE("CONTACT_MOBILE", "联系人手机号", 4),
        ;
        private final String value;
        private final String name;
        private final Integer ticketValue;
    }

    /**
     * 限制日期类型
     *
     * <AUTHOR>
     * @date 2025/09/03
     */
    @AllArgsConstructor
    @Getter
    enum LimitDateType {
        USAGE_DATE(TimeNodeEnum.USAGE_DATE),
        CREATE_DATE(TimeNodeEnum.CREATE_DATE),
        ;

        LimitDateType(TimeNodeEnum usageDate) {
            this.value = usageDate.getValue();
            this.name = usageDate.getName();
        }

        private final String value;
        private final String name;
    }
}
