package com.ly.ticketfun.etl.common.enums.ticket;

/**
 * 人群限制表限制类型枚举
 *
 * <AUTHOR>
 * @date 2024-5-13
 * @note
 */
public enum TravellerCrowdLimitEnums {
    /**
     * 人群限制-限制类型
     */
    Age(0, "年龄"),
    Gender(1, "性别"),
    SpecialArea(2, "特殊区域"),
    Birthday(3, "出生日期"),
    ChineseZodiac(4, "生肖"),
    Name(5, "姓名"),
    Student(6, "学生");

    private final Integer code;
    private final String message;

    TravellerCrowdLimitEnums(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static String getName(int code) {
        for (TravellerCrowdLimitEnums c : TravellerCrowdLimitEnums.values()) {
            if (c.getCode() == code) {
                return c.message;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
