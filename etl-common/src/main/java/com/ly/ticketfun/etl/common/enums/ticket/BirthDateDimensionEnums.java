package com.ly.ticketfun.etl.common.enums.ticket;

/**
 * 出生日期维度
 */
public enum BirthDateDimensionEnums {
    BirthdayYear(301, "出生日年"),
    BirthdayMonth(302, "出生日月"),
    BirthdayDay(303, "出生日日");

    BirthDateDimensionEnums(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    final Integer code;
    final String message;

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
