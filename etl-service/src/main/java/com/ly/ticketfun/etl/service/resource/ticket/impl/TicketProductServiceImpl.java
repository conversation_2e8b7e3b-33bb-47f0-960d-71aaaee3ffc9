package com.ly.ticketfun.etl.service.resource.ticket.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.localactivity.framework.enums.LogOperateTypeEnum;
import com.ly.localactivity.framework.utils.log.LogUtils;
import com.ly.ticketfun.etl.dataService.tczbyresource.IResourceBaseInfoService;
import com.ly.ticketfun.etl.dataService.tczbyresource.IResourceProductService;
import com.ly.ticketfun.etl.domain.tczbyresource.ResourceBaseInfo;
import com.ly.ticketfun.etl.domain.tczbyresource.ResourceProduct;
import com.ly.ticketfun.etl.domain.tczbyresource.agg.TicketProductAgg;
import com.ly.ticketfun.etl.service.resource.ticket.ITicketProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class TicketProductServiceImpl implements ITicketProductService {

    @Resource
    private IResourceProductService resourceProductService;

    @Resource
    private IResourceBaseInfoService resourceBaseInfoService;


    @Override
    public TicketProductAgg fetchProduct(Long poiId, Long productId){
        String traceId = productId.toString();
        TicketProductAgg ticketProductAgg = new TicketProductAgg();
        ResourceProduct resourceProduct = resourceProductService.queryOne(
                getResourceProductQueryWrapper(poiId, productId)
        );
        if (resourceProduct == null){
            LogUtils.info(LogOperateTypeEnum.QUERY,"资源产品信息不存在",0,traceId,"");
            return null;
        }
        ticketProductAgg.setResourceProduct(resourceProduct);
        ResourceBaseInfo resourceBaseInfo = resourceBaseInfoService.queryOne(
                getResourceBaseInfoQueryWrapper(poiId)
        );
        if (resourceBaseInfo == null){
            LogUtils.info(LogOperateTypeEnum.QUERY,"资源产品信息不存在",0,traceId,"");
            return null;
        }
        ticketProductAgg.setResourceBaseInfo(resourceBaseInfo);
        return ticketProductAgg;
    }

    private Wrapper<ResourceBaseInfo> getResourceBaseInfoQueryWrapper(Long poiId) {
        return new QueryWrapper<ResourceBaseInfo>()
                .eq("RBIId", poiId)
                .eq("RBIRowStatus",1);
    }

    private static QueryWrapper<ResourceProduct> getResourceProductQueryWrapper(Long poiId, Long productId) {
        return new QueryWrapper<ResourceProduct>()
                .eq("RPResourceId", poiId)
                        .eq("RPNId", productId)
                                .eq("RPRowStatus",1);
    }


}
