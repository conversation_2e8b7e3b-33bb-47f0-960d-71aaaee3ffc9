package com.ly.ticketfun.etl.service.resource.templateResource.transform.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.ly.localactivity.framework.enums.LogOperateTypeEnum;
import com.ly.localactivity.framework.utils.log.LogUtils;
import com.ly.ticketfun.etl.common.constant.TemplateResourceConstant;
import com.ly.ticketfun.etl.common.enums.mq.MQEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.SkuResourceEnum;
import com.ly.ticketfun.etl.common.enums.ticket.StockTypeEnum;
import com.ly.ticketfun.etl.common.exception.TRTransformException;
import com.ly.ticketfun.etl.dataService.tczbyresource.*;
import com.ly.ticketfun.etl.dataService.ticketFunInnerApi.ITicketFunInnerApiService;
import com.ly.ticketfun.etl.domain.common.GlobalRegionDateDto;
import com.ly.ticketfun.etl.domain.tczbyresource.*;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceSkuInfo;
import com.ly.ticketfun.etl.domain.templateResource.dto.SkuResourcePriceDto;
import com.ly.ticketfun.etl.domain.templateResource.ro.TRTransformResultRo;
import com.ly.ticketfun.etl.domain.templateResource.ro.TicketFunResourceChangeRo;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.ITRTransformService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service("ticketSkuPriceTransformService")
public class TicketSkuPriceTransformServiceImpl extends TRTransformBaseServiceImpl
        implements ITRTransformService<TicketFunTemplateResourceSkuInfo> {

    @Resource
    private IPolicyBaseInfoService policyBaseInfoService;

    @Resource
    private IPolicyPriceCalendarService policyPriceCalendarService;

    @Resource
    private IPolicyInventoryService policyInventoryService;

    @Resource
    private IPolicyProductRelationService policyProductRelationService;

    @Resource
    private IResourceBaseInfoService resourceBaseInfoService;

    @Resource
    private ITicketFunInnerApiService ticketFunInnerApiService;

    @Override
    public Boolean support(String resourceType, MQEnum.TicketFunResourceChangeCategory changeCategory) {
        return resourceType.equals(TemplateResourceConstant.ResourceType.TICKET)
                && changeCategory.equals(MQEnum.TicketFunResourceChangeCategory.SKU_PRICE_CHANGE);
    }

    @Override
    public TRTransformResultRo<TicketFunTemplateResourceSkuInfo> transformData(
            TicketFunResourceChangeRo changeRo,
            MQEnum.TicketFunResourceChangeCategory changeCategory
    ) {
        try {
            Long resourceId = changeRo.getPoiId();
            Long policyId = changeRo.getPackageId();
            if (resourceId == null || resourceId <= 0 || policyId == null || policyId <= 0) {
                return new TRTransformResultRo<>(TRTransformException.ErrorInfo.REQ_PARAMS_ERROR,
                        "景区Id或政策Id为空"
                );
            }

            // 获取景区基础信息
            ResourceBaseInfo resourceBaseInfo = resourceBaseInfoService.queryByResourceId(resourceId);
            if (resourceBaseInfo == null) {
                return new TRTransformResultRo<>(TRTransformException.ErrorInfo.RESOURCE_NOT_EXISTS);
            }

            // 获取政策基础信息
            PolicyBaseInfo policyBaseInfo = policyBaseInfoService.queryByResourceIdAndPolicyId(resourceId, policyId);
            if (policyBaseInfo == null) {
                return new TRTransformResultRo<>(TRTransformException.ErrorInfo.RESOURCE_NOT_EXISTS);
            }

            // 获取时区信息
            GlobalRegionDateDto globalRegionDateDto = ticketFunInnerApiService.globalRegionSearch(
                    resourceBaseInfo.getCountryId(),
                    resourceBaseInfo.getCityId()
            );
            if (globalRegionDateDto == null) {
                throw new TRTransformException(TRTransformException.ErrorInfo.SKU_TRANSFORM_FAIL,
                        String.valueOf(resourceId), String.valueOf(policyId), "getTimezone");
            }

            // 获取当地时间
            LocalDateTime nowDateTime = LocalDateTime.now(ZoneOffset.UTC);
            if (StringUtils.isNotEmpty(globalRegionDateDto.getTimeZone())) {
                nowDateTime = nowDateTime.plusHours(Long.parseLong(globalRegionDateDto.getTimeZone()));
            }

            // 查询政策价格日历
            List<PolicyPriceCalendar> policyPriceList =
                    policyPriceCalendarService.querySaleListByResourceIdAndPolicyId(
                            resourceId,
                            policyId,
                            nowDateTime.toLocalDate(),
                            null
                    );

            // 查询库存信息
            List<PolicyInventory> policyInventoryList = new ArrayList<>();
            List<Long> inventoryIdList = policyPriceList.stream()
                    .map(PolicyPriceCalendar::getInventoryId)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(inventoryIdList)) {
                policyInventoryList = policyInventoryService.queryListByIdList(resourceId, inventoryIdList);
            }

            return transformEntity(policyBaseInfo, policyPriceList, policyInventoryList, globalRegionDateDto);
        } catch (TRTransformException e) {
            return new TRTransformResultRo<>(e.getErrorInfo(), e.getExtendList());
        }
    }

    @Override
    public List<TRTransformResultRo<TicketFunTemplateResourceSkuInfo>> transformDataList(
            TicketFunResourceChangeRo changeRo,
            MQEnum.TicketFunResourceChangeCategory changeCategory
    ) {
        try {
            // 获取政策价格转换信息
            TRTransformResultRo<TicketFunTemplateResourceSkuInfo> resultRo = transformData(changeRo, changeCategory);
            if (resultRo == null || !resultRo.getIsSuccess()) {
                return new ArrayList<>(Collections.singletonList(resultRo));
            }

            Long resourceId = changeRo.getPoiId();
            Long policyId = changeRo.getPackageId();
            List<TRTransformResultRo<TicketFunTemplateResourceSkuInfo>> resultList = new ArrayList<>();

            // 获取政策绑定货架
            List<PolicyProductRelation> shelfRelationList = policyProductRelationService
                    .queryListByPolicyId(resourceId, policyId);
            for (PolicyProductRelation policyProductRelation : shelfRelationList) {
                // 拷贝
                TRTransformResultRo<TicketFunTemplateResourceSkuInfo> ro =
                        JSON.parseObject(JSON.toJSONString(resultRo),
                                new TypeReference<TRTransformResultRo<TicketFunTemplateResourceSkuInfo>>() {
                                }
                        );

                ro.getData().setProductId(String.valueOf(policyProductRelation.getProductId()));

                resultList.add(ro);
            }

            return resultList;
        } catch (TRTransformException e) {
            return new ArrayList<>(Collections.singletonList(
                    new TRTransformResultRo<>(e.getErrorInfo(), e.getExtendList())
            ));
        }
    }

    /**
     * 转换实体
     *
     * @param policyBaseInfo      政策基础信息
     * @param policyPriceList     政策价格列表
     * @param policyInventoryList 政策库存列表
     * @param globalRegionDateDto 时区信息
     * @return sku信息
     */
    private TRTransformResultRo<TicketFunTemplateResourceSkuInfo> transformEntity(
            PolicyBaseInfo policyBaseInfo,
            List<PolicyPriceCalendar> policyPriceList,
            List<PolicyInventory> policyInventoryList,
            GlobalRegionDateDto globalRegionDateDto
    ) {
        String resourceId = String.valueOf(policyBaseInfo.getResourceId());
        String policyId = String.valueOf(policyBaseInfo.getPolicyId());

        TicketFunTemplateResourceSkuInfo skuInfo = new TicketFunTemplateResourceSkuInfo();
        skuInfo.setResourceId(resourceId);
        skuInfo.setPackageId(policyId);
        skuInfo.setSkuId(policyId);

        // 转换币种信息
        transformCurrency(policyBaseInfo, skuInfo);
        // 转换价格列表
        transformPriceList(policyBaseInfo, policyPriceList, policyInventoryList, skuInfo, globalRegionDateDto);

        return new TRTransformResultRo<>(skuInfo);
    }

    /**
     * 转换价格列表
     *
     * @param policyBaseInfo      政策基础信息
     * @param policyPriceList     政策价格列表
     * @param policyInventoryList 政策库存列表
     * @param skuInfo             sku信息
     */
    private void transformPriceList(PolicyBaseInfo policyBaseInfo,
                                    List<PolicyPriceCalendar> policyPriceList,
                                    List<PolicyInventory> policyInventoryList,
                                    TicketFunTemplateResourceSkuInfo skuInfo,
                                    GlobalRegionDateDto globalRegionDateDto
    ) {
        try {
            List<SkuResourcePriceDto.PriceCalendarDto> priceList = new ArrayList<>();

            for (PolicyPriceCalendar policyPriceCalendar : policyPriceList) {
                SkuResourcePriceDto.PriceCalendarDto priceCalendarDto = new SkuResourcePriceDto.PriceCalendarDto();

                priceCalendarDto.setPriceDate(
                        policyPriceCalendar.getTravelDate()
                                .toEpochSecond(ZoneOffset.ofHours(Integer.parseInt(globalRegionDateDto.getTimeZone())))
                );
                priceCalendarDto.setSalePrice(policyPriceCalendar.getSaleAmount());
                priceCalendarDto.setNetPrice(policyPriceCalendar.getContractAmount());
                priceCalendarDto.setMarketPrice(policyPriceCalendar.getAmount());

                policyInventoryList.stream()
                        .filter(i -> Objects.equals(i.getId(), policyPriceCalendar.getInventoryId()))
                        .findFirst()
                        .ifPresent(inventoryInfo -> {
                            if (Objects.equals(inventoryInfo.getType(), StockTypeEnum.NO_LIMIT_STOCK.getCode())) {
                                priceCalendarDto.setStockType(SkuResourceEnum.StockType.NO_LIMITED_STOCK.getValue());
                            } else if (Objects.equals(inventoryInfo.getType(), StockTypeEnum.TOTAL_STOCK.getCode())
                                    || Objects.equals(inventoryInfo.getType(), StockTypeEnum.DAY_STOCK.getCode())
                            ) {
                                priceCalendarDto.setStockType(SkuResourceEnum.StockType.LIMITED_STOCK.getValue());
                                priceCalendarDto.setStockQuantity(
                                        inventoryInfo.getCount() - inventoryInfo.getUsedCount()
                                );
                            }
                        });
            }

            skuInfo.setPriceList(priceList);
        } catch (Exception e) {
            String resourceId = String.valueOf(policyBaseInfo.getResourceId());
            String policyId = String.valueOf(policyBaseInfo.getPolicyId());

            LogUtils.error(LogOperateTypeEnum.OTHER, e, resourceId, policyId);
            throw new TRTransformException(TRTransformException.ErrorInfo.SKU_TRANSFORM_FAIL,
                    resourceId, policyId, "priceList");
        }
    }

    /**
     * 转换币种信息
     *
     * @param policyBaseInfo 政策基础信息
     * @param skuInfo        sku信息
     */
    private void transformCurrency(PolicyBaseInfo policyBaseInfo, TicketFunTemplateResourceSkuInfo skuInfo) {
        try {
            skuInfo.setNetPriceCurrency(policyBaseInfo.getBalancePriceCurrencyCode());
            skuInfo.setSalePriceCurrency(policyBaseInfo.getSalePriceCurrencyCode());
            skuInfo.setMarketPriceCurrency(policyBaseInfo.getSalePriceCurrencyCode());

        } catch (Exception e) {
            String resourceId = String.valueOf(policyBaseInfo.getResourceId());
            String policyId = String.valueOf(policyBaseInfo.getPolicyId());

            LogUtils.error(LogOperateTypeEnum.OTHER, e, resourceId, policyId);
            throw new TRTransformException(TRTransformException.ErrorInfo.SKU_TRANSFORM_FAIL,
                    resourceId, policyId, "currency");
        }
    }
}
