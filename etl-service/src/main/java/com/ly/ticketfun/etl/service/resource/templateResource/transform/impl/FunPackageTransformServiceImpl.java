package com.ly.ticketfun.etl.service.resource.templateResource.transform.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.ly.localactivity.framework.enums.LogOperateTypeEnum;
import com.ly.localactivity.framework.utils.log.LogUtils;
import com.ly.localactivity.model.domain.tczbactivityresource.*;
import com.ly.localactivity.model.domain.tczbactivityresource.agg.*;
import com.ly.localactivity.model.enums.common.TimeCompareEnum;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.localactivity.model.enums.resource.ClauseEnum;
import com.ly.localactivity.model.enums.resource.TimeCompareRuleEnum;
import com.ly.localactivity.model.enums.tczbactivityresource.MainResourceBookFillInfoEnum;
import com.ly.localactivity.model.enums.tczbactivityresource.SubResourceTakeReturnEnum;
import com.ly.ticketfun.etl.common.constant.TemplateResourceConstant;
import com.ly.ticketfun.etl.common.enums.base.TimeNodeEnum;
import com.ly.ticketfun.etl.common.enums.mq.MQEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.*;
import com.ly.ticketfun.etl.common.exception.TRTransformException;
import com.ly.ticketfun.etl.dataService.ticketFunInnerApi.ITicketFunInnerApiService;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourcePackageInfo;
import com.ly.ticketfun.etl.domain.templateResource.dto.*;
import com.ly.ticketfun.etl.domain.templateResource.ro.TRTransformResultRo;
import com.ly.ticketfun.etl.domain.templateResource.ro.TicketFunResourceChangeRo;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.ITRTransformService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 玩乐套餐转换模版资源服务
 *
 * <AUTHOR>
 * @date 2025/08/28
 */
@Slf4j
@Service
public class FunPackageTransformServiceImpl extends FunBaseTransformFunctionServiceImpl implements ITRTransformService<TicketFunTemplateResourcePackageInfo> {

    @Resource
    private ITicketFunInnerApiService ticketFunInnerApiService;

    /**
     * 支持
     *
     * @param resourceType   资源类型
     * @param changeCategory 变更类别
     * @return {@link Boolean}
     */
    @Override
    public Boolean support(String resourceType, MQEnum.TicketFunResourceChangeCategory changeCategory) {
        return resourceType.equals(TemplateResourceConstant.ResourceType.FUN)
                && (changeCategory.equals(MQEnum.TicketFunResourceChangeCategory.PACKAGE_CHANGE)
                || changeCategory.equals(MQEnum.TicketFunResourceChangeCategory.TRAVEL_JOURNEY_CHANGE));
    }

    /**
     * 转换数据
     *
     * @param changeRo       更改ro
     * @param changeCategory 变更类别
     * @return {@link List}<{@link TRTransformResultRo}>
     */
    @Override
    public List<TRTransformResultRo<TicketFunTemplateResourcePackageInfo>> transformDataList(TicketFunResourceChangeRo changeRo, MQEnum.TicketFunResourceChangeCategory changeCategory) {
        // -------------- 入参校验 --------------
//        if (changeCategory.equals(MQEnum.TicketFunResourceChangeCategory.PACKAGE_CHANGE)
//                && ((changeRo.getProductId() == null || changeRo.getProductId() < 1) || (changeRo.getPackageId() == null || changeRo.getPackageId() < 1))) {
//            return new ArrayList<>(Arrays.asList(new TRTransformResultRo(TRTransformException.ErrorInfo.REQ_PARAMS_ERROR, "产品Id或套餐Id为空")));
//        }
//        if (changeCategory.equals(MQEnum.TicketFunResourceChangeCategory.TRAVEL_JOURNEY_CHANGE)
//                && ((changeRo.getProductId() == null || changeRo.getProductId() < 1) || (changeRo.getJourneyId() == null || changeRo.getJourneyId() < 1))) {
//            return new ArrayList<>(Arrays.asList(new TRTransformResultRo(TRTransformException.ErrorInfo.REQ_PARAMS_ERROR, "产品Id或套餐Id为空")));
//        }
        // -------------- 数据查询 --------------

        // -------------- 数据校验 --------------

        // -------------- 数据匹配 --------------

        // -------------- 数据返回 --------------
        return null;
    }

    /**
     * 匹配数据
     */
    public List<TRTransformResultRo<TicketFunTemplateResourcePackageInfo>> matchData(MainResourceAgg mainResourceAgg) {
        // -------------- 匹配共用数据 --------------
        // 1.品类
        ResourceCategoryDto resourceCategoryDto = parseCategoryFun().apply(mainResourceAgg.getMainResource().getFirstCategoryId(), mainResourceAgg.getMainResource().getSecondCategoryId());
        // 2.赠品清单
        List<ResourceGiftDto> giftList = parseGiftList(mainResourceAgg.getMainResourceGiftList());
        //3.退改规则
        RefundPolicyDto refundPolicyDto = parseRefundPolicy(mainResourceAgg.getCancelChangeRuleAgg());
        //4.服务提供方
        ResourceServiceProviderDto serviceProviderDto = parseServiceProvider(mainResourceAgg.getServiceProvider());
        //5.预定确定和凭证
        Tuple2<BookConfirmDto, BookVoucherDto> bookConfirmVoucher = parseBookConfirmVoucher(mainResourceAgg.getBookConfigAgg());
        //6.预订联系人信息
        BookContactInfoDto bookContactInfoDto = parseBookContactInfo(mainResourceAgg.getBookFillInfoAgg());
        //7.游客填写信息
        List<BookPassengerQuestionDto> bookPassengerQuestionList = parseCommonBookPassengerQuestion(mainResourceAgg.getBookFillInfoAgg());
        // todo...发票提供方

        return mainResourceAgg.getSubResourceAggList()
                .stream()
                .map(subResourceAgg -> {
                            TicketFunTemplateResourcePackageInfo packageResource = new TicketFunTemplateResourcePackageInfo();
                            try {
                                //共用数据
                                packageResource.setCategory(resourceCategoryDto);
                                packageResource.setGiftList(giftList);
                                packageResource.setRefundPolicy(refundPolicyDto);
                                packageResource.setServiceProvider(serviceProviderDto);
                                packageResource.setBookConfirm(bookConfirmVoucher.getT1());
                                packageResource.setBookVoucher(bookConfirmVoucher.getT2());
                                packageResource.setBookContactInfo(bookContactInfoDto);
                                packageResource.setBookPassengerQuestionList(new ArrayList<>(bookPassengerQuestionList));
                                packageResource.setCooperationType(SourceAndCooperationEnum.CooperationTypeEnum.DAILY_ENTERTAINMENT.getValue());

                                // -------------- 匹配基础信息 --------------
                                matchBaseInfo(subResourceAgg.getSubResource(), subResourceAgg.getDetail(), packageResource);
                                // -------------- 匹配销售属性 --------------
                                matchSaleProperty(subResourceAgg.getSetmealAttributeAggList(), packageResource);
                                // -------------- 配送信息--------------
                                matchDeliveryInfo(subResourceAgg.getDeliveryInfo(), packageResource);
                                // -------------- 附加费用--------------
                                matchSurchargeList(subResourceAgg.getSurchargeList(), packageResource);
                                // -------------- 解析人群-------------
                                List<ResourceBandInfoEnum.BandCode> crowdList = parseCrowdList(subResourceAgg.getSkuResourceAggList());
                                // -------------- 条款--------------
                                matchClauseList(subResourceAgg.getTravelJourneyAgg().getClauseAggList(), crowdList, packageResource);
                                // -------------- 预定限制--------------
                                matchBookLimit(subResourceAgg.getBookLimit(), mainResourceAgg.getBookConfigAgg().getBookConfig(), packageResource);
                                // -------------- 预定确认--------------
                                matchBookConfirm(subResourceAgg.getBookConfigAgg(), packageResource);
                                // -------------- 问卷--------------
                                matchBookPassengerQuestion(subResourceAgg.getPassengerQuestionList(), mainResourceAgg.getBookFillInfoAgg().getBookFillInfo().getTravellerInfoNeedType(), packageResource);
                                // -------------- 销售数据--------------
                                matchProductSalesSummaryList(subResourceAgg.getDetail(), packageResource);
                                // -------------- 逻辑拓展--------------
                                matchLogicExtendList(subResourceAgg.getLogicExtend(), packageResource);
                                // -------------- sku--------------
                                matchSkuList(subResourceAgg.getSkuResourceAggList(), packageResource);
                                // -------------- 线路--------------
                                matchTravelJourneyList(subResourceAgg.getTravelJourneyAgg(), packageResource);

                                return new TRTransformResultRo<>(packageResource);
                            } catch (TRTransformException e) {
                                return new TRTransformResultRo<TicketFunTemplateResourcePackageInfo>(e.getErrorInfo(), e.getExtendList());
                            } catch (Exception e) {
                                LogUtils.error(LogOperateTypeEnum.OTHER, e, subResourceAgg.getSubResource().getSerialId(), "package");
                                return new TRTransformResultRo<TicketFunTemplateResourcePackageInfo>(TRTransformException.ErrorInfo.PACKAGE_TRANSFORM_FAIL, packageResource.getProductId(), packageResource.getPackageId(), "");
                            }
                        }
                )
                .collect(Collectors.toList());
    }


    /**
     * 匹配旅行旅程列表
     *
     * @param travelJourneyAgg 旅行旅程agg
     * @param packageResource  软件包资源
     */
    private void matchTravelJourneyList(TravelJourneyAgg travelJourneyAgg, TicketFunTemplateResourcePackageInfo packageResource) {
        TravelJourney journey = travelJourneyAgg.getJourney();
        TravelJourneyDto travelJourneyDto = new TravelJourneyDto();
        travelJourneyDto.setJourneyId(journey.getId());
        travelJourneyDto.setJourneyName(journey.getJourneyName());
        travelJourneyDto.setJourneyDays(journey.getDayNum());
        travelJourneyDto.setDepartureCityId(journey.getLeavePortCityId());
        travelJourneyDto.setDepartureCityName(journey.getLeavePortCity());
        packageResource.setTravelJourneyList(Collections.singletonList(travelJourneyDto));
    }

    /**
     * 匹配sku列表
     *
     * @param skuResourceAggList sku资源agg列表
     * @param packageResource    软件包资源
     */
    private void matchSkuList(List<SkuResourceAgg> skuResourceAggList, TicketFunTemplateResourcePackageInfo packageResource) {
        try {
            long startTime = System.currentTimeMillis();
            LogUtils.info(LogOperateTypeEnum.OTHER, "开始匹配sku", 0, packageResource.getPackageId(), "package");

            List<SkuResourceDto> skuResourceDtoList = skuResourceAggList.stream()
                    .map(skuResourceAgg -> {
                                SkuResource skuResource = skuResourceAgg.getSkuResource();
                                MainResourceCrowd resourceCrowd = skuResourceAgg.getRelationCrowd();
                                SkuResourceDto skuResourceDto = new SkuResourceDto();
                                skuResourceDto.setProductId(packageResource.getProductId());
                                skuResourceDto.setPackageId(packageResource.getPackageId());
                                skuResourceDto.setSkuId(skuResource.getSerialId());
                                skuResourceDto.setName(skuResource.getName());
                                skuResourceDto.setUnitQuantity(skuResource.getUnitQuantity());
                                skuResourceDto.setUnitMaxQuantity(skuResource.getUnitMaxQuantity());
                                skuResourceDto.setBookMinQuantity(skuResource.getMinNumber());
                                skuResourceDto.setBookMaxQuantity(skuResource.getMaxNumber());
                                skuResourceDto.setSaleAloneType(funEnumMapping(SkuResourceEnum.SaleAloneType.class, skuResource.getSaleAloneType()).getValue());
                                skuResourceDto.setSaleStatus(CollectionUtils.size(skuResourceAgg.getSkuPriceList()) > 0 ? ProductResourceEnum.SaleStatus.CAN_SALE.getValue() : ProductResourceEnum.SaleStatus.CAN_NOT_SALE.getValue());
                                ResourceBandInfoDto bandInfoDto = mainResourceCrowd2ResourceBandInfoDto()
                                        .apply(resourceCrowd);
                                skuResourceDto.setBandInfo(Collections.singletonList(bandInfoDto));
                                return skuResourceDto;
                            }
                    )
                    .collect(Collectors.toList());
            packageResource.setSkuList(skuResourceDtoList);

            LogUtils.info(LogOperateTypeEnum.OTHER, "匹配sku结束,结果: " + JSON.toJSONString(skuResourceDtoList),
                    System.currentTimeMillis() - startTime, packageResource.getPackageId(), "package");

        } catch (TRTransformException e) {
            throw e;
        } catch (Exception e) {
            throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_TRANSFORM_FAIL, packageResource.getProductId(), packageResource.getPackageId(), "skuList");
        }
    }

    /**
     * 解析公共的问卷信息
     *
     * @param bookFillInfoAgg 书籍填充信息agg
     * @return {@link List }<{@link BookPassengerQuestionDto }>
     */
    private List<BookPassengerQuestionDto> parseCommonBookPassengerQuestion(MainResourceBookFillInfoAgg bookFillInfoAgg) {
        try {
            MainResourceBookFillInfo bookFillInfo = bookFillInfoAgg.getBookFillInfo();
            Map<Long, QuestionTypeInfo> questionTypeInfoMap = bookFillInfoAgg.getQuestionTypeInfoList().stream().collect(Collectors.toMap(QuestionTypeInfo::getId, Function.identity(), (a, b) -> a));
            return bookFillInfoAgg.getBookTravellerInfoList().stream()
                    .filter(bookTravellerInfo -> bookTravellerInfo.getTypeInfoId() != null)
                    .filter(bookTravellerInfo -> questionTypeInfoMap.containsKey(bookTravellerInfo.getTypeInfoId()))
                    .map(bookTravellerInfo -> {
                                BookPassengerQuestionDto bookPassengerQuestionDto = new BookPassengerQuestionDto();
                                QuestionTypeInfo questionTypeInfo = questionTypeInfoMap.get(bookTravellerInfo.getTypeInfoId());
                                bookPassengerQuestionDto.setQuestionType(funEnumMapping(ResourceBookPassengerInfoEnum.QuestionType.class, questionTypeInfo.getType()).getValue());
                                bookPassengerQuestionDto.setQuestionType(funEnumMapping(ResourceBookPassengerInfoEnum.QuestionType.class, questionTypeInfo.getType()).getValue());
                                bookPassengerQuestionDto.setPassengerRequiredType(funEnumMapping(ResourceBookPassengerInfoEnum.PassengerRequiredType.class, bookFillInfo.getTravellerInfoNeedType()).getValue());
                                bookPassengerQuestionDto.setQuestionId(questionTypeInfo.getId());
                                bookPassengerQuestionDto.setQuestionCode(funEnumMapping(ResourceBookPassengerInfoEnum.QuestionInfo.class, questionTypeInfo.getCode()).getValue());
                                bookPassengerQuestionDto.setQuestionTitle(questionTypeInfo.getName());
                                bookPassengerQuestionDto.setDataTypes(questionTypeInfo.getDataTypes());
                                return bookPassengerQuestionDto;
                            }
                    ).collect(Collectors.toList());
        } catch (TRTransformException e) {
            throw e;
        } catch (Exception e) {
            LogUtils.error(LogOperateTypeEnum.OTHER, e, bookFillInfoAgg.getBookFillInfo().getMainResourceSerialId(), "");
            throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_TRANSFORM_FAIL, bookFillInfoAgg.getBookFillInfo().getMainResourceSerialId(), "", "bookPassengerQuestion");
        }
    }

    private void matchProductSalesSummaryList(SubResourceDetail ignore, TicketFunTemplateResourcePackageInfo ignore2) {
        //todo
//        LogicExtendDto monthSaleNum = new LogicExtendDto(LogicExtendEnum.key.COMMENT_SCORE.getValue(), String.valueOf(detail.getMonthSaleNum()));

    }

    private void matchLogicExtendList(SubResourceLogicExtend logicExtend, TicketFunTemplateResourcePackageInfo packageResource) {
        // todo.... 逻辑拓展存放哪些数据
        LogicExtendDto hasQuestionnaire = new LogicExtendDto(LogicExtendEnum.key.HAS_QUESTIONNAIRE.getValue(), String.valueOf(logicExtend.getHadPassengerQuestion()));
        if (CollectionUtils.isEmpty(packageResource.getLogicExtendList())) {
            packageResource.setLogicExtendList(new ArrayList<>());
        }
        packageResource.getLogicExtendList().add(hasQuestionnaire);
    }


    /**
     * 匹配旅客问卷
     *
     * @param passengerQuestionList 旅客问题列表
     * @param travellerInfoNeedType 旅客信息需求类型
     * @param packageResource       套餐资源
     */
    private void matchBookPassengerQuestion(List<PassengerQuestionAgg> passengerQuestionList, Integer travellerInfoNeedType, TicketFunTemplateResourcePackageInfo packageResource) {
        if (CollectionUtils.isEmpty(passengerQuestionList)) {
            return;
        }
        try {
            long startTime = System.currentTimeMillis();
            LogUtils.info(LogOperateTypeEnum.OTHER, "开始匹配问卷", 0, packageResource.getPackageId(), "passengerQuestion");
            List<BookPassengerQuestionDto> bookPassengerQuestionList = passengerQuestionList.stream().map(passengerQuestionAgg -> {
                BookPassengerQuestionDto bookPassengerQuestionDto = new BookPassengerQuestionDto();
                QuestionTypeInfo questionTypeInfo = passengerQuestionAgg.getQuestionTypeInfo();
                bookPassengerQuestionDto.setQuestionType(funEnumMapping(ResourceBookPassengerInfoEnum.QuestionType.class, questionTypeInfo.getType()).getValue());
                bookPassengerQuestionDto.setPassengerRequiredType(funEnumMapping(ResourceBookPassengerInfoEnum.PassengerRequiredType.class, travellerInfoNeedType).getValue());
                bookPassengerQuestionDto.setQuestionId(questionTypeInfo.getId());
                bookPassengerQuestionDto.setQuestionCode(funEnumMapping(ResourceBookPassengerInfoEnum.QuestionInfo.class, questionTypeInfo.getCode()).getValue());
                bookPassengerQuestionDto.setQuestionTitle(questionTypeInfo.getName());
                bookPassengerQuestionDto.setDataTypes(questionTypeInfo.getDataTypes());
                bookPassengerQuestionDto.setRemark(passengerQuestionAgg.getQuestion().getInputTip());
                bookPassengerQuestionDto.setQuestionItemList(passengerQuestionAgg.getQuestionItemList()
                        .stream()
                        .map(questionItem -> new BookPassengerQuestionDto.QuestionItemDto(questionItem.getId(), questionItem.getValue()))
                        .collect(Collectors.toList())
                );
                return bookPassengerQuestionDto;
            }).collect(Collectors.toList());
            packageResource.getBookPassengerQuestionList().addAll(bookPassengerQuestionList);

            LogUtils.info(LogOperateTypeEnum.OTHER, "匹配问卷结束,结果: " + JSON.toJSONString(bookPassengerQuestionList),
                    System.currentTimeMillis() - startTime, packageResource.getPackageId(), "package");
        } catch (Exception e) {
            LogUtils.error(LogOperateTypeEnum.OTHER, e, packageResource.getPackageId(), "package");
            throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_TRANSFORM_FAIL, packageResource.getProductId(), packageResource.getPackageId(), "bookPassengerQuestion");
        }
    }

    /**
     * 解析图书联系信息
     *
     * @param bookFillInfoAgg 书籍填充信息agg
     * @return {@link BookContactInfoDto }
     */
    private BookContactInfoDto parseBookContactInfo(MainResourceBookFillInfoAgg bookFillInfoAgg) {
        MainResourceBookFillInfo bookFillInfo = bookFillInfoAgg.getBookFillInfo();
        BookContactInfoDto bookContactInfo = new BookContactInfoDto();
        bookContactInfo.setNameRequired(WhetherEnum.YES.getValue());
        for (String contactInfo : bookFillInfo.getContactInfo().split(StrUtil.COMMA)) {
            if (!StrUtil.isNumeric(contactInfo)) {
                continue;
            }
            if (Integer.valueOf(contactInfo).equals(MainResourceBookFillInfoEnum.ContactInfoType.MOBILE.getValue())) {
                bookContactInfo.setMobileRequired(WhetherEnum.YES.getValue());
            } else {
                bookContactInfo.setMobileRequired(WhetherEnum.NO.getValue());
            }
            if (Integer.valueOf(contactInfo).equals(MainResourceBookFillInfoEnum.ContactInfoType.EMAIL.getValue())) {
                bookContactInfo.setEmailRequired(WhetherEnum.YES.getValue());
            } else {
                bookContactInfo.setEmailRequired(WhetherEnum.NO.getValue());
            }
            //todo..非大陆电子邮件?
        }
        return bookContactInfo;
    }

    /**
     * 解析预定确认
     *
     * @param bookConfigAgg 图书配置agg
     * @return {@link BookConfirmDto }
     */
    private Tuple2<BookConfirmDto, BookVoucherDto> parseBookConfirmVoucher(MainResourceBookConfigAgg bookConfigAgg) {
        try {
            MainResourceBookConfig bookConfig = bookConfigAgg.getBookConfig();
            BookConfirmDto bookConfirmDto = getBookConfirmDto(bookConfig);
            BookVoucherDto bookVoucherDto = new BookVoucherDto();
            bookVoucherDto.setSendVoucher(bookConfig.getSendVoucher());
            bookVoucherDto.setVoucherSender(funEnumMapping(BookVoucherEnum.VoucherSender.class, bookConfig.getSendVoucherType()).getValue());
            //todo..凭证使用方式
            return Tuples.of(bookConfirmDto, bookVoucherDto);
        } catch (Exception e) {
            throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_TRANSFORM_FAIL, bookConfigAgg.getBookConfig().getMainResourceSerialId(), bookConfigAgg.getBookConfig().getSubResourceSerialId(), "bookConfirm");
        }
    }

    private BookConfirmDto getBookConfirmDto(MainResourceBookConfig bookConfig) {
        BookConfirmDto bookConfirmDto = new BookConfirmDto();
        bookConfirmDto.setConfirmFirstMode(funEnumMapping(BookConfirmEnum.ConfirmFirstMode.class, bookConfig.getConfirmFirstMode()).getValue());
        bookConfirmDto.setConfirmSecondMode(funEnumMapping(BookConfirmEnum.ConfirmSecondMode.class, bookConfig.getConfirmSecondMode()).getValue());
        bookConfirmDto.setConfirmThirdMode(funEnumMapping(BookConfirmEnum.ConfirmThirdMode.class, bookConfig.getConfirmThirdMode()).getValue());
        bookConfirmDto.setConfirmDurationType(funEnumMapping(BookConfirmEnum.ConfirmDurationType.class, bookConfig.getConfirmDurationType()).getValue());
        bookConfirmDto.setConfirmMinutes(bookConfig.getConfirmDurationMinute());
        return bookConfirmDto;
    }

    /**
     * 解析服务提供程序
     *
     * @param serviceProvider 服务提供商
     * @return {@link ResourceServiceProviderDto }
     */
    private ResourceServiceProviderDto parseServiceProvider(MainResourceServiceProvider serviceProvider) {
        if (serviceProvider == null) {
            return null;
        }
        try {
            long startTime = System.currentTimeMillis();
            LogUtils.info(LogOperateTypeEnum.OTHER, "开始匹配服务提供方", 0, "", "package");
            ResourceServiceProviderDto serviceProviderDto = new ResourceServiceProviderDto();
            serviceProviderDto.setBrandType(funEnumMapping(ResourceServiceProviderEnum.Provider.class, serviceProvider.getBrandType()).getValue());
            serviceProviderDto.setBrandName(serviceProvider.getBrandName());
            serviceProviderDto.setVendorName(serviceProvider.getVendorName());
            serviceProviderDto.setPreServiceProvider(funEnumMapping(ResourceServiceProviderEnum.Provider.class, serviceProvider.getPreServiceProvider()).getValue());
            serviceProviderDto.setPreServiceWorkDay(serviceProvider.getPreServiceWorkDay());
            serviceProviderDto.setPreServicePhone(serviceProvider.getPreServicePhone());
            serviceProviderDto.setAfterServiceProvider(funEnumMapping(ResourceServiceProviderEnum.Provider.class, serviceProvider.getAfterServiceProvider()).getValue());
            serviceProviderDto.setAfterServiceWorkDay(serviceProvider.getAfterServiceWorkDay());
            serviceProviderDto.setAfterServicePhone(serviceProvider.getAfterServicePhone());
            serviceProviderDto.setLicense(serviceProvider.getLicense());
            serviceProviderDto.setBusinessLicense(serviceProvider.getBusinessLicense());
            LogUtils.info(LogOperateTypeEnum.OTHER, "匹配服务提供方结束,结果: " + JSON.toJSONString(serviceProviderDto),
                    System.currentTimeMillis() - startTime, "", "package");
            return serviceProviderDto;
        } catch (TRTransformException e) {
            throw e;
        } catch (Exception e) {
            throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_TRANSFORM_FAIL, serviceProvider.getMainResourceSerialId(), "", "serviceProvider");
        }
    }


    /**
     * 解析退款政策
     *
     * @param cancelChangeRuleAgg 取消更改规则agg
     * @return {@link RefundPolicyDto }
     */
    private RefundPolicyDto parseRefundPolicy(CancelChangeRuleAgg cancelChangeRuleAgg) {
        RefundPolicyDto refundPolicy = new RefundPolicyDto();
        CancelChangeRule changeRule = cancelChangeRuleAgg.getRule();
        refundPolicy.setRefundType(funEnumMapping(RefundPolicyEnum.RefundType.class, changeRule.getRuleType()).getValue());
        refundPolicy.setTimeLimitType(funEnumMapping(RefundPolicyEnum.TimeLimitType.class, changeRule.getDateType()).getValue());
        refundPolicy.setPartialRefund(changeRule.getIsCanSingle());
        //todo.. 过期退
        refundPolicy.setOverdueRefund(WhetherEnum.NO.getValue());
        refundPolicy.setAdditionalNote(changeRule.getAdditionalNote());
        refundPolicy.setItemList(cancelChangeRuleAgg.getRuleDetailList().stream().map(ruleDetail -> {
                    RefundPolicyDto.RefundPolicyItemDto itemDto = new RefundPolicyDto.RefundPolicyItemDto();
                    //todo.. 比较时间类型
                    itemDto.setCompareDays(ruleDetail.getDay());
                    itemDto.setCompareHour(ruleDetail.getHour());
                    itemDto.setCompareMinute(ruleDetail.getHour());
                    TimeCompareEnum timeCompareEnum = enumMapping(TimeCompareEnum.class, ruleDetail.getTimeAfter());
                    if (TimeCompareEnum.AFTER.equals(timeCompareEnum)) {
                        itemDto.setCompareTimeAfter(TimeCompareRuleEnum.AFTER_EXCLUDE_CURRENT.getCode());
                    } else if (TimeCompareEnum.BEFORE.equals(timeCompareEnum)) {
                        itemDto.setCompareTimeAfter(TimeCompareRuleEnum.BEFORE_INCLUDE_CURRENT.getCode());
                    }

                    itemDto.setCostType(funEnumMapping(RefundPolicyEnum.CostType.class, ruleDetail.getFeeType()).getValue());
                    //数字格式
                    itemDto.setCostValue(String.valueOf(ruleDetail.getFee()));
                    itemDto.setCostCurrency(ruleDetail.getCostCurrency());
                    itemDto.setDescription(ruleDetail.getDescription());
                    return itemDto;
                }).collect(Collectors.toList())
        );
        return refundPolicy;
    }

    /**
     * 匹配礼物列表
     *
     * @param mainResourceGiftList 主要资源礼品列表
     * @return {@link List }<{@link ResourceGiftDto }>
     */
    private List<ResourceGiftDto> parseGiftList(List<MainResourceGift> mainResourceGiftList) {
        if (CollectionUtils.isEmpty(mainResourceGiftList)) {
            return null;
        }
        try {
            return mainResourceGiftList.stream().map(mainResourceGift -> {
                ResourceGiftDto resourceGiftDto = new ResourceGiftDto();
                resourceGiftDto.setContent(mainResourceGift.getContent());
                resourceGiftDto.setValidityTimeType(funEnumMapping(ResourceGiftEnum.ValidityTimeType.class, mainResourceGift.getValidityTimeType()).getValue());
                resourceGiftDto.setValidityBeginTime(LocalDateTimeUtil.toEpochMilli(mainResourceGift.getValidityBeginTime()));
                resourceGiftDto.setValidityEndTime(LocalDateTimeUtil.toEpochMilli(mainResourceGift.getValidityBeginTime()));
                return resourceGiftDto;
            }).collect(Collectors.toList());

        } catch (Exception ex) {
            throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_TRANSFORM_FAIL, mainResourceGiftList.get(0).getMainResourceSerialId(), "", "giftList");
        }
    }

    private void matchBookConfirm(MainResourceBookConfigAgg bookConfigAgg, TicketFunTemplateResourcePackageInfo packageResourceDto) {
        try {
            long startTime = System.currentTimeMillis();
            LogUtils.info(LogOperateTypeEnum.OTHER, "开始套餐匹配预定确认", 0, packageResourceDto.getPackageId(), "package");
            Optional.ofNullable(bookConfigAgg.getBookConfig())
                    .map(this::getBookConfirmDto)
                    .ifPresent(packageResourceDto::setBookConfirm);
            LogUtils.info(LogOperateTypeEnum.OTHER, "套餐匹配预定确认结束,结果: " + JSON.toJSONString(packageResourceDto.getBookConfirm()),
                    System.currentTimeMillis() - startTime, packageResourceDto.getPackageId(), "package");
        } catch (Exception ex) {
            LogUtils.error(LogOperateTypeEnum.OTHER, ex, packageResourceDto.getPackageId(), "package");
            throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_TRANSFORM_FAIL, packageResourceDto.getProductId(), packageResourceDto.getPackageId(), "bookConfirm");
        }
    }

    /**
     * 匹配预定限制
     *
     * @param bookLimit          预定限制
     * @param bookConfig         预定配置
     * @param packageResourceDto 套餐资源
     */
    private void matchBookLimit(ResourceBookLimit bookLimit, MainResourceBookConfig bookConfig, TicketFunTemplateResourcePackageInfo packageResourceDto) {
        try {
            long startTime = System.currentTimeMillis();
            LogUtils.info(LogOperateTypeEnum.OTHER, "开始匹配预定限制", 0, packageResourceDto.getPackageId(), "package");
            BookLimitDto bookLimitDto = new BookLimitDto();
            bookLimitDto.setNeedAdvance(WhetherEnum.YES.getValue());
            bookLimitDto.setAdvanceBookDays(bookLimit.getAdvanceDay());
            bookLimitDto.setAdvanceBookHour(bookLimit.getAdvanceHour());
            bookLimitDto.setAdvanceBookMinute(bookLimit.getAdvanceMinute());
            bookLimitDto.setBookMaxDay(bookLimit.getMaxDay());
            bookLimitDto.setBookMinDay(bookLimit.getMinDay());
            bookLimitDto.setBookMinQuantity(bookLimit.getMinPerson());
            bookLimitDto.setBookMaxQuantity(bookLimit.getMaxPerson());
            bookLimitDto.setPayTimeoutMinutes(bookConfig.getPayConfirmMinute());

            packageResourceDto.setBookLimit(bookLimitDto);
            LogUtils.info(LogOperateTypeEnum.OTHER, "匹配预定限制结束,结果: " + JSON.toJSONString(bookLimitDto),
                    System.currentTimeMillis() - startTime, packageResourceDto.getPackageId(), "package");
        } catch (TRTransformException ex) {
            throw ex;
        } catch (Exception ex) {
            LogUtils.error(LogOperateTypeEnum.OTHER, ex, packageResourceDto.getPackageId(), "package");
            throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_TRANSFORM_FAIL, packageResourceDto.getProductId(), packageResourceDto.getPackageId(), "bookLimit");
        }

    }

    /**
     * 解析人群列表
     *
     * @param skuResourceAggList sku资源agg列表
     * @return {@link List }<{@link ResourceBandInfoDto }>
     */
    private List<ResourceBandInfoEnum.BandCode> parseCrowdList(List<SkuResourceAgg> skuResourceAggList) {
        return skuResourceAggList.stream().map(SkuResourceAgg::getRelationCrowd)
                .map(resourceCrowd ->
                        funEnumMapping(ResourceBandInfoEnum.BandCode.class, resourceCrowd.getCode())
                ).distinct()
                .collect(Collectors.toList());
    }

    /**
     * 匹配条款
     *
     * @param clauseAggList      匹配条款
     * @param crowdList          人群列表
     * @param packageResourceDto 套餐资源
     */
    private void matchClauseList(List<TravelJourneyClauseAgg> clauseAggList, List<ResourceBandInfoEnum.BandCode> crowdList, TicketFunTemplateResourcePackageInfo packageResourceDto) {
        try {
            if (CollectionUtils.isEmpty(clauseAggList)) {
                return;
            }
            long startTime = System.currentTimeMillis();
            LogUtils.info(LogOperateTypeEnum.OTHER, "开始匹配条款", 0, packageResourceDto.getPackageId(), "package");
            List<ClauseTypeDto> clauseTypeDtoList = clauseAggList.stream().collect(Collectors.groupingBy(resourceClauseAgg -> resourceClauseAgg.getClause().getTypeCode()))
                    .entrySet()
                    .stream()
                    .map(entry -> {
                                String clauseType = entry.getKey();
                                List<TravelJourneyClauseAgg> mainResourceClauses = entry.getValue();
                                ClauseTypeDto clauseTypeDto = new ClauseTypeDto();
                                clauseTypeDto.setClauseTypeCode(clauseType);
                                clauseTypeDto.setClauseAvailableCount(mainResourceClauses.size());
                                ClauseEnum.TypeCodeEnum typeCodeEnum = enumMapping(ClauseEnum.TypeCodeEnum.class, clauseType);
                                clauseTypeDto.setTitle(typeCodeEnum.getName());
                                List<ClauseTypeDto.ClauseDto> clauses = mainResourceClauses
                                        .stream()
                                        .map(travelJourneyClauseAgg -> {
                                                    ClauseTypeDto.ClauseDto clauseDto = new ClauseTypeDto.ClauseDto();
                                                    TravelJourneyClause clause = travelJourneyClauseAgg.getClause();
                                                    clauseDto.setClauseCode(clause.getClauseCode());
                                                    clauseDto.setTitle(clause.getClauseName());
                                                    List<ClauseTypeDto.ClauseItemDto> clauseItemList = travelJourneyClauseAgg.getClauseItemAggList()
                                                            .stream()
                                                            .map(travelJourneyClauseItemAgg -> {
                                                                        ClauseTypeDto.ClauseItemDto clauseItemDto = new ClauseTypeDto.ClauseItemDto();
                                                                        TravelJourneyClauseItem clauseItem = travelJourneyClauseItemAgg.getClauseItem();
                                                                        clauseItemDto.setClauseItemCode(clauseItem.getCode());
                                                                        clauseItemDto.setTitle(clauseItem.getCreateName());
                                                                        clauseItemDto.setContent(clauseItem.getDescription());
                                                                        String crowdCodes = Optional.ofNullable(clauseItem.getCrowdCodes()).filter(StrUtil::isNotBlank).orElse(crowdList.stream().map(ResourceBandInfoEnum.BandCode::getValue).collect(Collectors.joining(StrUtil.COMMA)));
                                                                        clauseItemDto.setSuitCrowdCodes(crowdCodes);
                                                                        return clauseItemDto;
                                                                    }
                                                            ).collect(Collectors.toList());
                                                    clauseDto.setClauseItemList(clauseItemList);
                                                    return clauseDto;
                                                }
                                        ).collect(Collectors.toList());
                                clauseTypeDto.setClauseList(clauses);
                                return clauseTypeDto;
                            }
                    ).collect(Collectors.toList());
            packageResourceDto.setClauseList(clauseTypeDtoList);
            LogUtils.info(LogOperateTypeEnum.OTHER, "匹配条款结束,结果: " + JSON.toJSONString(clauseTypeDtoList),
                    System.currentTimeMillis() - startTime, packageResourceDto.getPackageId(), "package");
        } catch (TRTransformException ex) {
            throw ex;
        } catch (Exception ex) {
            LogUtils.error(LogOperateTypeEnum.OTHER, ex, packageResourceDto.getPackageId(), "package");
            throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_TRANSFORM_FAIL, packageResourceDto.getProductId(), packageResourceDto.getPackageId(), "clauseList");
        }

    }

    /**
     * 匹配附加费列表
     *
     * @param surchargeList      附加费清单
     * @param packageResourceDto 套餐资源
     */
    private void matchSurchargeList(List<SubResourceSurcharge> surchargeList, TicketFunTemplateResourcePackageInfo packageResourceDto) {
        try {
            if (CollectionUtils.isEmpty(surchargeList)) {
                return;
            }
            long startTime = System.currentTimeMillis();
            LogUtils.info(LogOperateTypeEnum.OTHER, "开始匹配附加费用", 0, packageResourceDto.getPackageId(), "package");
            List<ResourceSurchargeDto> resourceSurchargeList = surchargeList.stream().map(subResourceSurcharge -> {
                ResourceSurchargeDto resourceSurchargeDto = new ResourceSurchargeDto();
                ResourceSurchargeEnum.FeeType feeType = funEnumMapping(ResourceSurchargeEnum.FeeType.class, subResourceSurcharge.getFeeType());
                resourceSurchargeDto.setFeeType(feeType.getValue());
                resourceSurchargeDto.setCollectType(funEnumMapping(ResourceSurchargeEnum.CollectType.class, subResourceSurcharge.getCollectType()).getValue());
                resourceSurchargeDto.setCollectWay(funEnumMapping(ResourceSurchargeEnum.CollectWay.class, subResourceSurcharge.getCollectWay()).getValue());
                resourceSurchargeDto.setCostBear(funEnumMapping(ResourceSurchargeEnum.CostBear.class, subResourceSurcharge.getCostBear()).getValue());
                resourceSurchargeDto.setRefundType(funEnumMapping(ResourceSurchargeEnum.RefundType.class, subResourceSurcharge.getCostIsRefund()).getValue());
                //todo..附加费用币种

                resourceSurchargeDto.setAmount(subResourceSurcharge.getAmount());
                return resourceSurchargeDto;
            }).collect(Collectors.toList());
            packageResourceDto.setSurchargeList(resourceSurchargeList);
            LogUtils.info(LogOperateTypeEnum.OTHER, "匹配附加费用结束,结果: " + JSON.toJSONString(resourceSurchargeList),
                    System.currentTimeMillis() - startTime, packageResourceDto.getPackageId(), "package");
        } catch (TRTransformException ex) {
            throw ex;
        } catch (Exception ex) {
            LogUtils.error(LogOperateTypeEnum.OTHER, ex, packageResourceDto.getPackageId(), "package");
            throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_TRANSFORM_FAIL, packageResourceDto.getProductId(), packageResourceDto.getPackageId(), "surcharge");
        }
    }

    /**
     * 匹配配送信息
     *
     * @param subResourceTakeReturn 子资源获取返回
     * @param packageResourceDto    套餐资源
     */
    private void matchDeliveryInfo(SubResourceTakeReturn subResourceTakeReturn, TicketFunTemplateResourcePackageInfo packageResourceDto) {
        try {
            if (subResourceTakeReturn == null) {
                return;
            }
            long startTime = System.currentTimeMillis();
            LogUtils.info(LogOperateTypeEnum.OTHER, "开始匹配配送信息", 0, packageResourceDto.getPackageId(), "package");

            ResourceDeliveryInfoDto deliveryInfo = new ResourceDeliveryInfoDto();
            deliveryInfo.setDeliveryType(funEnumMapping(ResourceDeliveryInfoEnum.DeliveryType.class, subResourceTakeReturn.getTakeType()).getValue());
            SubResourceTakeReturnEnum.LatestDeliveryTimeNode latestDeliveryTimeNode = enumMapping(SubResourceTakeReturnEnum.LatestDeliveryTimeNode.class, subResourceTakeReturn.getLatestDeliveryTimeNode());
            SubResourceTakeReturnEnum.LatestDeliveryTimeAfter latestDeliveryTimeAfter = enumMapping(SubResourceTakeReturnEnum.LatestDeliveryTimeAfter.class, subResourceTakeReturn.getLatestDeliveryTimeAfter());
            TimeNodeEnum timeNodeEnum = Optional.ofNullable(TimeNodeEnum.fromFunEnum(latestDeliveryTimeNode, latestDeliveryTimeAfter)).orElseThrow(() -> new TRTransformException(TRTransformException.ErrorInfo.ENUM_NOT_MAPPING, TimeNodeEnum.class.getSimpleName(), subResourceTakeReturn.getLatestDeliveryTimeNode() + "_" + subResourceTakeReturn.getLatestDeliveryTimeAfter()));
            deliveryInfo.setDeliveryTimeNode(timeNodeEnum.getValue());
            deliveryInfo.setDeliveryTimeDays(subResourceTakeReturn.getLatestDeliveryTimeDay());
            deliveryInfo.setDeliveryTimeHour(subResourceTakeReturn.getLatestDeliveryTimeHour());
            deliveryInfo.setDeliveryTimeMinute(subResourceTakeReturn.getLatestDeliveryTimeMinute());
            packageResourceDto.setDeliveryInfo(deliveryInfo);
            LogUtils.info(LogOperateTypeEnum.OTHER, "匹配配送信息结束,结果: " + JSON.toJSONString(deliveryInfo),
                    System.currentTimeMillis() - startTime, packageResourceDto.getPackageId(), "package");
        } catch (TRTransformException ex) {
            throw ex;
        } catch (Exception ex) {
            LogUtils.error(LogOperateTypeEnum.OTHER, ex, packageResourceDto.getPackageId(), "package");
            throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_TRANSFORM_FAIL, packageResourceDto.getProductId(), packageResourceDto.getPackageId(), "deliveryInfo");
        }
    }

    /**
     * 匹配销售属性
     *
     * @param setmealOptionsRelationAggList 套餐选项关系总表
     * @param packageResourceDto            包资源dto
     */
    private void matchSaleProperty(List<SetmealAttributeAgg> setmealOptionsRelationAggList, TicketFunTemplateResourcePackageInfo packageResourceDto) {
        try {
            //todo...销售属性
            List<PackageSalePropertyDto> packageSalePropertyList = setmealOptionsRelationAggList.stream().map(setmealAttributeAgg -> {
                SetmealAttribute setmealAttribute = setmealAttributeAgg.getSetmealAttribute();
                PackageSalePropertyDto packageSalePropertyDto = new PackageSalePropertyDto();
                packageSalePropertyDto.setPropertyId(setmealAttribute.getId());
                packageSalePropertyDto.setPropertyName(setmealAttribute.getAttributeName());
                packageSalePropertyDto.setPropertyCode(setmealAttribute.getCode());
                setmealAttributeAgg.getSetmealOptionsAggList()
                        .stream()
                        .findFirst()
                        .ifPresent(setmealOptionsAgg -> {
                            SetmealOptions setmealOptions = setmealOptionsAgg.getSetmealOptions();
                            packageSalePropertyDto.setPropertyValueId(String.valueOf(setmealOptions.getId()));
                            packageSalePropertyDto.setPropertyValueName(setmealOptions.getOptionName());
                        });
                return packageSalePropertyDto;
                //TODO..子销售属性?
            }).collect(Collectors.toList());
            packageResourceDto.setSalePropertyList(packageSalePropertyList);
        } catch (Exception ex) {
            LogUtils.error(LogOperateTypeEnum.OTHER, ex, packageResourceDto.getPackageId(), "package");
            throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_TRANSFORM_FAIL, packageResourceDto.getProductId(), packageResourceDto.getPackageId(), "saleProperty");
        }
    }

    /**
     * 匹配基础信息
     *
     */
    private void matchBaseInfo(SubResource subResource, SubResourceDetail subResourceDetail, PackageResourceDto packageResource) {
        try {
            packageResource.setProductId(subResource.getMainResourceSerialId());
            packageResource.setPackageId(subResource.getSerialId());
            packageResource.setName(subResource.getTitle());
            //服务语言
            packageResource.setServiceLanguageList(JSON.parseArray(subResource.getLanguage(), String.class));
            packageResource.setSaleStatus(WhetherEnum.YES.getValue().equals(subResourceDetail.getIsCanAdvance()) ? ProductResourceEnum.SaleStatus.CAN_SALE.getValue() : ProductResourceEnum.SaleStatus.CAN_NOT_SALE.getValue());
            packageResource.setCreateTime(LocalDateTimeUtil.toEpochMilli(subResource.getCreateDate()));
        } catch (Exception ex) {
            LogUtils.error(LogOperateTypeEnum.OTHER, ex, subResource.getSerialId(), "package");
            throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_TRANSFORM_FAIL, subResource.getMainResourceSerialId(), subResource.getSerialId(), "baseInfo");
        }
    }

}
