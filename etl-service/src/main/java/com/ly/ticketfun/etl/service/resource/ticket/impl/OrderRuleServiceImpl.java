package com.ly.ticketfun.etl.service.resource.ticket.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.ticketfun.etl.common.Utils.JsonUtils;
import com.ly.ticketfun.etl.common.enums.base.GenderEnum;
import com.ly.ticketfun.etl.common.enums.base.ZodiacEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.BookModeEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.BookQuotaLimitEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.ResourceBandInfoEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.ResourceBookPassengerInfoEnum;
import com.ly.ticketfun.etl.common.enums.ticket.*;
import com.ly.ticketfun.etl.dataService.tczbyresource.*;
import com.ly.ticketfun.etl.domain.bo.LimitCondition;
import com.ly.ticketfun.etl.domain.tczbyresource.*;
import com.ly.ticketfun.etl.domain.templateResource.dto.*;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.impl.TRTransformBaseServiceImpl;
import com.ly.ticketfun.etl.service.resource.ticket.IOrderRuleService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class OrderRuleServiceImpl extends TRTransformBaseServiceImpl implements IOrderRuleService {

    @Resource
    private IPolicyOrderModeService policyOrderModeService;
    @Resource
    private IPolicyOrderLimitService policyOrderLimitService;
    @Resource
    private IPolicyOrderLimitRuleService policyOrderLimitRuleService;
    @Resource
    private IPolicyTravellerBaseService policyTravellerBaseService;
    @Resource
    private IPolicyTravellerCrowdInfoService policyTravellerCrowdInfoService;
    @Resource
    private IPolicyTravellerCrowdLimitService policyTravellerCrowdLimitService;
    @Resource
    private IPolicyUseRuleService policyUseRuleService;
    @Resource
    private IPolicyUseRuleAdressService policyUseRuleAddressService;
    @Resource
    private IPolicyRefundRuleService policyRefundRuleService;
    @Resource
    private IPolicyRefundRuleDetailService policyRefundRuleDetailService;
    @Resource
    private IPolicyMultipleOptionsService policyMultipleOptionsService;
    @Resource
    private IPolicyBaseInfoService policyBaseInfoService;

    @Override
    public void toTemplateResource(Long resourceId, Long policyId) throws IOException {
        QueryWrapper<PolicyOrderMode> orderModeQueryWrapper = of(PolicyOrderMode.class, policyId, resourceId);
        PolicyOrderMode orderMode = policyOrderModeService.queryOne(orderModeQueryWrapper);

        QueryWrapper<PolicyOrderLimit> orderLimitQueryWrapper = of(PolicyOrderLimit.class, policyId, resourceId);
        PolicyOrderLimit orderLimit = policyOrderLimitService.queryOne(orderLimitQueryWrapper);

        QueryWrapper<PolicyOrderLimitRule> orderLimitRuleQueryWrapper = of(PolicyOrderLimitRule.class, policyId, resourceId);
        List<PolicyOrderLimitRule> orderLimitRuleList = policyOrderLimitRuleService.queryList(orderLimitRuleQueryWrapper);

        QueryWrapper<PolicyTravellerBase> travelBaseQueryWrapper = of(PolicyTravellerBase.class, policyId, resourceId);
        PolicyTravellerBase travellerBase = policyTravellerBaseService.queryOne(travelBaseQueryWrapper);

        QueryWrapper<PolicyTravellerCrowdInfo> travelCrowdInfoQueryWrapper = of(PolicyTravellerCrowdInfo.class, policyId, resourceId);
        List<PolicyTravellerCrowdInfo> crowdInfoList = policyTravellerCrowdInfoService.queryList(travelCrowdInfoQueryWrapper);

        QueryWrapper<PolicyTravellerCrowdLimit> travelCrowdLimitQueryWrapper = of(PolicyTravellerCrowdLimit.class, policyId, resourceId);
        List<PolicyTravellerCrowdLimit> travellerCrowdLimitList = policyTravellerCrowdLimitService.queryList(travelCrowdLimitQueryWrapper);

        QueryWrapper<PolicyUseRule> useRuleQueryWrapper = of(PolicyUseRule.class, policyId, resourceId);
        PolicyUseRule useRule = policyUseRuleService.queryOne(useRuleQueryWrapper);

        QueryWrapper<PolicyUseRuleAdress> useRuleAddressQueryWrapper = of(PolicyUseRuleAdress.class, policyId, resourceId);
        List<PolicyUseRuleAdress> useRuleAdressesList = policyUseRuleAddressService.queryList(useRuleAddressQueryWrapper);

        QueryWrapper<PolicyRefundRule> refundRuleQueryWrapper = of(PolicyRefundRule.class, policyId, resourceId);
        PolicyRefundRule refundRule = policyRefundRuleService.queryOne(refundRuleQueryWrapper);

        QueryWrapper<PolicyRefundRuleDetail> refundRuleDetailQueryWrapper = of(PolicyRefundRuleDetail.class, policyId, resourceId);
        List<PolicyRefundRuleDetail> refundRuleDetailList = policyRefundRuleDetailService.queryList(refundRuleDetailQueryWrapper);

        QueryWrapper<PolicyMultipleOptions> multipleOptionsQueryWrapper = of(PolicyMultipleOptions.class, policyId, resourceId);
        List<PolicyMultipleOptions> policyMultipleOptions = policyMultipleOptionsService.queryList(multipleOptionsQueryWrapper);

        QueryWrapper<PolicyBaseInfo> policyQueryWrapper = of(PolicyBaseInfo.class, policyId, resourceId);
        PolicyBaseInfo policyBaseInfo = policyBaseInfoService.queryOne(policyQueryWrapper);


        BookModeDto bookModeDto = orderMode(orderMode, policyBaseInfo);
        BookLimitDto bookLimitDto = orderLimit(orderLimit, orderLimitRuleList);
        //游玩人信息+是否需要游玩人信息
        List<BookPassengerQuestionDto> bookPassengerQuestionList = travellerInfo(policyMultipleOptions, travellerBase);
        //适用人群
        List<ResourceBandInfoDto> bandInfoList = bandInfoList(crowdInfoList, travellerCrowdLimitList, travellerBase);
        //购票人信息
        BookContactInfoDto bookContactInfoDto = travellerContact(policyMultipleOptions);

        UseRuleDto useRuleDto = useRule(useRule, useRuleAdressesList);

    }

    private UseRuleDto useRule(PolicyUseRule useRule, List<PolicyUseRuleAdress> useRuleAdressesList) {
        return null;
    }

    private List<ResourceBandInfoDto> bandInfoList(List<PolicyTravellerCrowdInfo> crowdInfoList, List<PolicyTravellerCrowdLimit> travellerCrowdLimitList, PolicyTravellerBase travellerBase) throws IOException {
        List<ResourceBandInfoDto> list = new ArrayList<>();
        for (PolicyTravellerCrowdInfo crowd : crowdInfoList) {
            ResourceBandInfoDto band = new ResourceBandInfoDto();
            band.setBandCode(ticketEnumMapping(ResourceBandInfoEnum.BandCode.class, crowd.getCrowdType().toString(), false).getValue());
            band.setBandTitle(ticketEnumMapping(ResourceBandInfoEnum.BandCode.class, crowd.getCrowdType().toString(), false).getName());
            band.setPersonQuantity(crowd.getNum());
            Integer needAllCrowd = travellerBase.getNeedAllCrowd();
            band.setPassengerRequiredType(needAllCrowd == 0 ?
                    ResourceBookPassengerInfoEnum.PassengerRequiredType.REQUIRED_ALL.getValue()
                    : ResourceBookPassengerInfoEnum.PassengerRequiredType.REQUIRED_ONE.getValue());
            band.setBandLimitRuleList(getBandLimitList(travellerCrowdLimitList));
            list.add(band);
        }
        return list;
    }

    private List<ResourceBandInfoDto.BandLimitRuleDto> getBandLimitList(List<PolicyTravellerCrowdLimit> travellerCrowdLimitList) throws IOException {
        List<ResourceBandInfoDto.BandLimitRuleDto> list = new ArrayList<>();
        Map<Long, List<PolicyTravellerCrowdLimit>> map = travellerCrowdLimitList.stream().collect(Collectors.groupingBy(PolicyTravellerCrowdLimit::getGroupId));
        for (List<PolicyTravellerCrowdLimit> value : map.values()) {
            ResourceBandInfoDto.BandLimitRuleDto bandLimitRuleDto = new ResourceBandInfoDto.BandLimitRuleDto();
            for (PolicyTravellerCrowdLimit limit : value) {
                if (limit.getLimitType().equals(TravellerCrowdLimitEnums.Age.getCode())) {
                    //region年龄
                    ResourceBandInfoDto.AgeLimitRuleDto ageLimitRuleDto = new ResourceBandInfoDto.AgeLimitRuleDto();
                    ageLimitRuleDto.setCanBook(limit.getCanBook() == 1 ? 1 : 0);
                    ageLimitRuleDto.setAgeCalcBirthdayDimension(ticketEnumMapping(ResourceBandInfoEnum.BirthdayDimension.class, limit.getDimensionType(), false).getValue());
                    ageLimitRuleDto.setAgeCalcCompareDimension("");
                    List<LimitCondition> limitConditions = JsonUtils.string2GenericAbstract(limit.getLimitConditions(), new TypeReference<List<LimitCondition>>() {
                    });
                    for (LimitCondition limitCondition : limitConditions) {
                        String contentFirst = limitCondition.getContentFirst();
                        String contentSecond = limitCondition.getContentSecond();
                        String contentFirstLimit = limitCondition.getContentFirstLimit();
                        String contentSecondLimit = limitCondition.getContentSecondLimit();
                        ResourceBandInfoDto.AgeRangeDto ageRangeDto = new ResourceBandInfoDto.AgeRangeDto(
                                StringUtils.isNotEmpty(contentFirst) ? Integer.valueOf(contentFirst) : null,
                                StringUtils.isNotEmpty(contentFirstLimit) ? Integer.valueOf(contentFirstLimit) : null,
                                StringUtils.isNotEmpty(contentSecond) ? Integer.valueOf(contentSecond) : null,
                                StringUtils.isNotEmpty(contentSecondLimit) ? Integer.valueOf(contentSecondLimit) : null
                        );
                        ageLimitRuleDto.getAgeRangeList().add(ageRangeDto);
                    }
                    bandLimitRuleDto.getAgeLimitRuleList().add(ageLimitRuleDto);
                    //endregion
                }
                if (limit.getLimitType().equals(TravellerCrowdLimitEnums.Gender.getCode())) {
                    //region性别
                    List<LimitCondition> limitConditions = JsonUtils.string2GenericAbstract(limit.getLimitConditions(), new TypeReference<List<LimitCondition>>() {
                    });
                    if (!CollectionUtils.isEmpty(limitConditions)) {
                        String contentFirst = limitConditions.get(0).getContentFirst();
                        if (StringUtils.isNotEmpty(contentFirst)) {
                            ResourceBandInfoDto.GenderLimitRuleDto genderLimitRuleDto = new ResourceBandInfoDto.GenderLimitRuleDto(
                                    1,
                                    ticketEnumMapping(GenderEnum.class, Integer.valueOf(contentFirst), false).getValue()
                            );
                            bandLimitRuleDto.getGenderLimitRuleList().add(genderLimitRuleDto);
                        }
                    }
                    //endregion
                }
                if (limit.getLimitType().equals(TravellerCrowdLimitEnums.SpecialArea.getCode())) {
                    //region 特殊区域
                    ResourceBandInfoDto.AreaLimitRuleDto areaRuleDto = new ResourceBandInfoDto.AreaLimitRuleDto();
                    areaRuleDto.setCanBook(limit.getCanBook() == 1 ? 1 : 0);
                    areaRuleDto.setAreaDimension(limit.getDimensionType().toString());
                    List<LimitCondition> limitConditions = JsonUtils.string2GenericAbstract(limit.getLimitConditions(), new TypeReference<List<LimitCondition>>() {
                    });
                    List<String> cityIds = limitConditions.stream().map(LimitCondition::getContentFirst).collect(Collectors.toList());
                    areaRuleDto.setValueList(cityIds);
                    bandLimitRuleDto.getAreaLimitRuleList().add(areaRuleDto);
                    //endregion
                }
                if (limit.getLimitType().equals(TravellerCrowdLimitEnums.Birthday.getCode())) {
                    //region 出生日期
                    ResourceBandInfoDto.BirthdayLimitRuleDto birthRuleDto = new ResourceBandInfoDto.BirthdayLimitRuleDto();
                    birthRuleDto.setCanBook(limit.getCanBook() == 1 ? 1 : 0);
                    birthRuleDto.setDayDimension(getDimension(limit.getDimensionType()));
                    List<LimitCondition> limitConditions = JsonUtils.string2GenericAbstract(limit.getLimitConditions(), new TypeReference<List<LimitCondition>>() {
                    });
                    List<String> values = limitConditions.stream().map(LimitCondition::getContentFirst).collect(Collectors.toList());
                    birthRuleDto.setValueList(values);
                    bandLimitRuleDto.getBirthdayLimitRuleList().add(birthRuleDto);
                    //endregion
                }
                if (limit.getLimitType().equals(TravellerCrowdLimitEnums.ChineseZodiac.getCode())) {
                    //region 生肖
                    ResourceBandInfoDto.ZodiacLimitRuleDto zodiacRuleDto = new ResourceBandInfoDto.ZodiacLimitRuleDto();
                    zodiacRuleDto.setCanBook(limit.getCanBook() == 1 ? 1 : 0);
                    List<LimitCondition> limitConditions = JsonUtils.string2GenericAbstract(limit.getLimitConditions(), new TypeReference<List<LimitCondition>>() {
                    });
                    List<String> values = limitConditions.stream()
                            .map(LimitCondition::getContentFirst)
                            .map(s -> ZodiacEnum.getValue(Integer.valueOf(s)))
                            .collect(Collectors.toList());
                    zodiacRuleDto.setZodiacTypeList(values);
                    bandLimitRuleDto.getZodiacLimitRuleList().add(zodiacRuleDto);
                    //endregion
                }
                if (limit.getLimitType().equals(TravellerCrowdLimitEnums.Name.getCode())) {
                    //region 姓名
                    ResourceBandInfoDto.NameLimitRuleDto nameRuleDto = new ResourceBandInfoDto.NameLimitRuleDto();
                    nameRuleDto.setCanBook(limit.getCanBook() == 1 ? 1 : 0);
                    List<LimitCondition> limitConditions = JsonUtils.string2GenericAbstract(limit.getLimitConditions(), new TypeReference<List<LimitCondition>>() {
                    });
                    List<String> values = limitConditions.stream()
                            .map(LimitCondition::getContentFirst)
                            .collect(Collectors.toList());
                    nameRuleDto.setValueList(values);
                    bandLimitRuleDto.getNameLimitRuleList().add(nameRuleDto);
                    //endregion
                }
                if (limit.getLimitType().equals(TravellerCrowdLimitEnums.Student.getCode())) {
                    //region 学生
                    List<LimitCondition> limitConditions = JsonUtils.string2GenericAbstract(limit.getLimitConditions(), new TypeReference<List<LimitCondition>>() {
                    });
                    if (!CollectionUtils.isEmpty(limitConditions)) {
                        LimitCondition limitCondition = limitConditions.get(0);
                        Integer code = Integer.valueOf(limitCondition.getContentFirst());
                        ResourceBandInfoDto.IdentityAuthLimitRuleDto studentRuleDto = new ResourceBandInfoDto.IdentityAuthLimitRuleDto();
                        studentRuleDto.setCanBook(1);
                        studentRuleDto.setIdentityType(ResourceBandInfoEnum.IdentityType.getValue(code));
                        bandLimitRuleDto.getIdentityAuthLimitRuleDtoList().add(studentRuleDto);
                    }
                    //endregion
                }
            }
            list.add(bandLimitRuleDto);
        }
        return list;
    }

    private String getDimension(Integer dimensionType) {
        if (dimensionType.equals(BirthDateDimensionEnums.BirthdayYear.getCode())) {
            return ResourceBandInfoEnum.BirthdayDimension.YEAR.getValue();
        } else if (dimensionType.equals(BirthDateDimensionEnums.BirthdayMonth.getCode())) {
            return ResourceBandInfoEnum.BirthdayDimension.MONTH.getValue();
        } else if (dimensionType.equals(BirthDateDimensionEnums.BirthdayDay.getCode())) {
            return ResourceBandInfoEnum.BirthdayDimension.DAY.getValue();
        }
        return "";
    }

    private List<BookPassengerQuestionDto> travellerInfo(List<PolicyMultipleOptions> policyMultipleOptions, PolicyTravellerBase travellerBase) {
        List<BookPassengerQuestionDto> list = new ArrayList<>();
        List<PolicyMultipleOptions> collect = policyMultipleOptions.stream()
                .filter(o ->
                        o.getSelectTypeId().equals(PolicyMultipleOptionsEnums.TravellerInfoIdType.getCode())
                                || o.getSelectTypeId().equals(PolicyMultipleOptionsEnums.BasicInformationOfTraveller.getCode()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            for (PolicyMultipleOptions option : collect) {
                BookPassengerQuestionDto optionDto = new BookPassengerQuestionDto();
                optionDto.setQuestionType(getOptionType(option.getSelectTypeId()));
                optionDto.setPassengerRequiredType(ticketEnumMapping(ResourceBookPassengerInfoEnum.PassengerRequiredType.class, travellerBase.getNeedAllInfo(), false).getValue());
                optionDto.setQuestionCode(getQuestionCode(option.getSelectValue()));
                optionDto.setDataTypes(ResourceBookPassengerInfoEnum.DataType.MULTIPLE_ENUM.getName());
                list.add(optionDto);
            }
        }
        return list;
    }

    private String getQuestionCode(Integer selectValue) {
        if (selectValue.equals(TravellerIdCardTypeEnums.Id_Card.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionInfo.C_ID_CARD.getValue();
        } else if (selectValue.equals(TravellerIdCardTypeEnums.Passport.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionInfo.C_PASSPORT.getValue();
        } else if (selectValue.equals(TravellerIdCardTypeEnums.GanAo.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionInfo.C_HM_PASS.getValue();
        } else if (selectValue.equals(TravellerIdCardTypeEnums.TaiWan.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionInfo.C_TW_PASS.getValue();
        } else if (selectValue.equals(TravellerIdCardTypeEnums.HuiXiang.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionInfo.C_BACK_HOME_CARD.getValue();
        } else if (selectValue.equals(TravellerIdCardTypeEnums.YongJu.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionInfo.C_PERMANENT_RESIDENCE_CARD_FOREIGNERS.getValue();
        } else if (selectValue.equals(TravellerBaseInfoTypeEnums.NAME.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionInfo.B_CN_NAME.getValue();
        } else if (selectValue.equals(TravellerBaseInfoTypeEnums.CHINA_MOBILE.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionInfo.B_TEL_PHONE.getValue();
        } else if (selectValue.equals(TravellerBaseInfoTypeEnums.FOREIGN_MOBILE.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionInfo.B_TEL_PHONE_INTERNATIONAL.getValue();
        }
        return "";
    }


    private String getOptionType(Integer selectTypeId) {
        if (selectTypeId.equals(PolicyMultipleOptionsEnums.TravellerInfoIdType.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionType.CREDENTIALS.getValue();
        } else if (selectTypeId.equals(PolicyMultipleOptionsEnums.BasicInformationOfTraveller.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionType.BASIC.getValue();
        }
        return null;
    }

    private BookContactInfoDto travellerContact(List<PolicyMultipleOptions> policyMultipleOptions) {
        BookContactInfoDto bookContactInfoDto = new BookContactInfoDto();
        bookContactInfoDto.setNameRequired(0);
        bookContactInfoDto.setMobileRequired(0);
        bookContactInfoDto.setOverseaMobileRequired(0);
        bookContactInfoDto.setEmailRequired(0);
        List<PolicyMultipleOptions> collect = policyMultipleOptions.stream()
                .filter(o -> o.getSelectTypeId().equals(PolicyMultipleOptionsEnums.ContactUserInfo.getCode()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            if (collect.stream().anyMatch(c -> c.getSelectValue().equals(TravellerLinkerInfoTypeEnums.EMAIL.getCode()))) {
                bookContactInfoDto.setEmailRequired(1);
            }
            if (collect.stream().anyMatch(c -> c.getSelectValue().equals(TravellerLinkerInfoTypeEnums.MAINLAND.getCode()))) {
                bookContactInfoDto.setMobileRequired(1);
            }
            if (collect.stream().anyMatch(c -> c.getSelectValue().equals(TravellerLinkerInfoTypeEnums.NOT_MAINLAND.getCode()))) {
                bookContactInfoDto.setOverseaMobileRequired(1);
            }
            if (collect.stream().anyMatch(c -> c.getSelectValue().equals(TravellerLinkerInfoTypeEnums.NAME.getCode()))) {
                bookContactInfoDto.setNameRequired(1);
            }
        }
        return bookContactInfoDto;
    }

    private BookLimitDto orderLimit(PolicyOrderLimit orderLimit, List<PolicyOrderLimitRule> orderLimitRuleList) {
        BookLimitDto bookLimitDto = new BookLimitDto();
        bookLimitDto.setNeedAdvance(WhetherEnum.YES.getValue());
        bookLimitDto.setAdvanceBookDays(orderLimit.getInAdvanceDays());
        bookLimitDto.setAdvanceBookHour(orderLimit.getInAdvanceTime().getHour());
        bookLimitDto.setAdvanceBookMinute(orderLimit.getInAdvanceTime().getMinute());
        bookLimitDto.setAdvanceBookDescription("");
        bookLimitDto.setBookEffectMinutes(new BigDecimal(orderLimit.getInAdvanceHours() * 60 + orderLimit.getInAdvanceMinutes()));
        bookLimitDto.setPayTimeoutMinutes(orderLimit.getPayLimitMinute());
        bookLimitDto.setBookDaysFromCurrent(orderLimit.getOrderDaysLimit());
        bookLimitDto.setBookMinQuantity(orderLimit.getMinBuyNum());
        bookLimitDto.setBookMaxQuantity(orderLimit.getMaxBuyNum());
        List<BookQuotaLimitDto> limitRuleList = new ArrayList<>();
        orderLimitRuleList.forEach(rule -> {
            BookQuotaLimitDto ruleDto = new BookQuotaLimitDto();
            ruleDto.setGroupId(rule.getGroupId().toString());
            ruleDto.setLimitTargetType(ticketEnumMapping(BookQuotaLimitEnum.LimitTargetType.class, rule.getIdType(), false).getValue());
            ruleDto.setLimitTargetDesc("");
            ruleDto.setLimitDateType(rule.getDateType() == 0 ? BookQuotaLimitEnum.LimitDateType.USAGE_DATE.getValue() : BookQuotaLimitEnum.LimitDateType.CREATE_DATE.getValue());
            ruleDto.setLimitPeriodDays(rule.getDays().toString());
            ruleDto.setQuotaQuantity(rule.getNum().toString());
            ruleDto.setQuotaUnit(UnitEnums.getV(rule.getUnit()));
            limitRuleList.add(ruleDto);
        });
        bookLimitDto.setBookQuotaLimitList(limitRuleList);
        return bookLimitDto;
    }


    private BookModeDto orderMode(PolicyOrderMode orderMode, PolicyBaseInfo policyBaseInfo) {
        Integer ifAppointDate = orderMode.getIfAppointDate();
        Integer usePeriodType = orderMode.getUsePeriodType();
        Integer validDays = orderMode.getValidDays();
        Integer validDaysUnit = orderMode.getValidDaysUnit();
        LocalDateTime validityPeriodStart = orderMode.getValidityPeriodStart();
        LocalDateTime validityPeriodEnd = orderMode.getValidityPeriodEnd();
        Integer enterLimitType = orderMode.getEnterLimitType();
        BookModeDto bookModeDto = new BookModeDto();
        if (ifAppointDate == 1) {
            //指定日
            if (usePeriodType.equals(BookModeEnum.UsagePeriodType.ON_THAT_DAY.getTicketValue())) {
                //当日
                bookModeDto.setPriceMode(BookModeEnum.PriceMode.SPECIFY_SINGLE_USAGE_DATE.getValue());
                bookModeDto.setUsagePeriodType(BookModeEnum.UsagePeriodType.ON_THAT_DAY.getValue());
            } else {
                //指定日期起
                bookModeDto.setPriceMode(BookModeEnum.PriceMode.SPECIFY_MULTIPLE_USAGE_DATE.getValue());
                bookModeDto.setUsagePeriodType(BookModeEnum.UsagePeriodType.FROM_SPECIFY_DATE_ONWARD.getValue());
                if (validDaysUnit == 0) {
                    bookModeDto.setValidityDays(validDays.toString());
                } else {
                    bookModeDto.setValidityDays(String.valueOf(validDays * 365));
                }
            }
        } else {
            //无需指定日
            if (usePeriodType.equals(BookModeEnum.UsagePeriodType.SPECIFY_VALIDITY_PERIOD.getTicketValue())) {
                //指定有效期
                bookModeDto.setPriceMode(BookModeEnum.PriceMode.NOT_SPECIFY_USAGE_DATE.getValue());
                bookModeDto.setUsagePeriodType(BookModeEnum.UsagePeriodType.SPECIFY_VALIDITY_PERIOD.getValue());
                bookModeDto.setValidityPeriodStart(validityPeriodStart.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
                bookModeDto.setValidityPeriodEnd(validityPeriodEnd.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
            } else {
                //下单日期起
                bookModeDto.setPriceMode(BookModeEnum.PriceMode.NOT_SPECIFY_USAGE_DATE.getValue());
                bookModeDto.setUsagePeriodType(BookModeEnum.UsagePeriodType.FROM_ORDER_DATE_ONWARD.getValue());
                if (validDaysUnit == 0) {
                    bookModeDto.setValidityDays(validDays.toString());
                } else {
                    bookModeDto.setValidityDays(String.valueOf(validDays * 365));
                }
            }
        }
        bookModeDto.setBeyondValidityPeriodCanUse(enterLimitType == 2 ? 1 : 0);
        bookModeDto.setPolicyMode(ticketEnumMapping(BookModeEnum.PolicyMode.class, policyBaseInfo.getPolicyMode(), false).getValue());
        // 预约抢 使用日期限制  次数//TODO
        return bookModeDto;
    }

    public static <T> QueryWrapper<T> of(Class<T> entityClass, Long policyId, Long resourceId) {
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        return wrapper.eq("policy_id", policyId)
                .eq("resource_id", resourceId)
                .eq("row_status", 1);
    }
}
