package com.ly.ticketfun.etl.service.resource.templateResource.transform.impl;

import com.google.common.collect.Lists;
import com.ly.ticketfun.etl.common.constant.TemplateResourceConstant;
import com.ly.ticketfun.etl.common.enums.mq.MQEnum;
import com.ly.ticketfun.etl.common.exception.TRTransformException;
import com.ly.ticketfun.etl.dataService.ticketFunInnerApi.ITicketFunInnerApiService;
import com.ly.ticketfun.etl.domain.common.GlobalRegionDateDto;
import com.ly.ticketfun.etl.domain.tczbyresource.agg.TicketProductAgg;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceProductInfo;
import com.ly.ticketfun.etl.domain.templateResource.dto.ResourceCategoryDto;
import com.ly.ticketfun.etl.domain.templateResource.ro.TRTransformResultRo;
import com.ly.ticketfun.etl.domain.templateResource.ro.TicketFunResourceChangeRo;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.ITRTransformService;
import com.ly.ticketfun.etl.service.resource.ticket.ITicketProductService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Service
public class TicketProductTransformServiceImpl extends TRTransformBaseServiceImpl implements ITRTransformService<TicketFunTemplateResourceProductInfo> {

    @Resource
    private ITicketProductService ticketProductService;

    @Resource
    private ITicketFunInnerApiService ticketFunInnerApiService;

    @Override
    public Boolean support(String resourceType, MQEnum.TicketFunResourceChangeCategory changeCategory) {
        return TemplateResourceConstant.ResourceType.TICKET.equals(resourceType)
                && changeCategory.equals(MQEnum.TicketFunResourceChangeCategory.PRODUCT_CHANGE);
    }

    @Override
    public TRTransformResultRo<TicketFunTemplateResourceProductInfo> transformData(TicketFunResourceChangeRo changeRo, MQEnum.TicketFunResourceChangeCategory changeCategory) {
        if (changeRo == null || changeRo.getProductId() == null || changeRo.getProductId() <= 0
        || changeRo.getPoiId() == null || changeRo.getPoiId() <= 0){
            return new TRTransformResultRo<>(TRTransformException.ErrorInfo.REQ_PARAMS_ERROR);
        }
        // fetch product agg info
        TicketProductAgg productAgg = fetchProductInfo(changeRo.getPoiId(),changeRo.getProductId());
        if (productAgg == null){
            return new TRTransformResultRo<>(TRTransformException.ErrorInfo.RESOURCE_NOT_EXISTS);
        }
        // transform
        TicketFunTemplateResourceProductInfo templateResourceProductInfo = transformCore(productAgg);


        return null;
    }

    private TicketFunTemplateResourceProductInfo transformCore(TicketProductAgg productAgg) {
        TicketFunTemplateResourceProductInfo productTemplateInfo = getProductSimpleInfo(productAgg);
        // 门票品类
        productTemplateInfo.setCategory(getTicketDefaultCategory());
        // 行程天数
        productTemplateInfo.setJourneyDays(getTicketDefaultJourneyDays());
        // 出发地目的地信息
        productTemplateInfo.setDepartureList(Collections.emptyList());
        productTemplateInfo.setDestinationList(Collections.emptyList());
        productTemplateInfo.setPoiIdList(Lists.newArrayList(productAgg.getResourceProduct().getResourceId()));
        // todo时区
        productTemplateInfo.setTimeZone(fetchTimeZoneByTcCityId(productAgg.getResourceBaseInfo().getCountryId(),productAgg.getResourceBaseInfo().getCityId()));
        return productTemplateInfo;
    }

    private Integer fetchTimeZoneByTcCityId(Integer countryId,Integer cityId) {
        GlobalRegionDateDto globalRegionDateDto = ticketFunInnerApiService.globalRegionSearch(countryId,cityId);
        return Optional
                .ofNullable(globalRegionDateDto)
                .map(t -> t.getTimeZone())
                .map(t -> NumberUtils.toInt(t, 8))
                .orElse(8);
    }

    private List<String> getTicketDefaultJourneyDays() {
        return Lists.newArrayList("1");
    }

    private ResourceCategoryDto getTicketDefaultCategory() {
        ResourceCategoryDto resourceCategoryDto = new ResourceCategoryDto();
        resourceCategoryDto.setCategoryId(10L);
        resourceCategoryDto.setCategoryName("门票");
        return resourceCategoryDto;
    }

    private TicketFunTemplateResourceProductInfo getProductSimpleInfo(TicketProductAgg productAgg) {
        TicketFunTemplateResourceProductInfo templateResourceProductInfo = new TicketFunTemplateResourceProductInfo();
        templateResourceProductInfo.setProductId(productAgg.getResourceProduct().getId().toString());
        templateResourceProductInfo.setName(productAgg.getResourceProduct().getProductName());
        templateResourceProductInfo.setSubName("");
        return templateResourceProductInfo;
    }

    private TicketProductAgg fetchProductInfo(Long poiId,Long productId) {
        return ticketProductService.fetchProduct(poiId, productId);

    }
}
